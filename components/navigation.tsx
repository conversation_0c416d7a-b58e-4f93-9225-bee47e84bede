"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import Image from "next/image" // Import Image component
import { Camera, BarChart3, User, Menu, X, CircleUserRound, Languages } from "lucide-react" // Import Globe icon
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { SignedIn, SignedOut, UserButton, SignInButton, useUser } from "@clerk/nextjs" // Import useUser
import { useTranslation } from 'react-i18next'; // Import useTranslation
import i18n from '@/lib/i18n'; // Import i18n instance
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select" // Import Select components
import { useClerkSupabase } from '@/lib/supabaseClient'; // Import useClerkSupabase

const mainNavigation = [
  { name: "How it Works", href: "#how-it-works" },
  { name: "About Us", href: "#about" },
  { name: "Pricing", href: "#pricing" }, // Added Pricing link
  { name: "Testimonials", href: "#testimonials" },
  { name: "Contact", href: "#contact" },
]

const loggedInNavigation = [
  { name: "Analyze", href: "/dashboard/analyze", icon: Camera },
  { name: "History", href: "/dashboard/insights", icon: BarChart3 },
  { name: "Profile", href: "/dashboard/profile", icon: CircleUserRound },
]

export default function Navigation() {
  const { t } = useTranslation(); // Initialize useTranslation hook
  const { user, isLoaded } = useUser(); // Get Clerk user
  const supabase = useClerkSupabase(); // Get Supabase client

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [activeSection, setActiveSection] = useState("")
  const pathname = usePathname()
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language); // State for current language

  useEffect(() => {
    if (pathname === "/") {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              setActiveSection(entry.target.id)
            }
          })
        },
        { threshold: 0.5, rootMargin: "-50% 0px -50% 0px" }, // Adjust threshold to highlight when section is roughly in middle
      )

      mainNavigation.forEach((item) => {
        const id = item.href.substring(1) // Remove '#'
        const element = document.getElementById(id)
        if (element) {
          observer.observe(element)
        }
      })

      return () => {
        mainNavigation.forEach((item) => {
          const id = item.href.substring(1)
          const element = document.getElementById(id)
          if (element) {
            observer.unobserve(element)
          }
        })
      }
    } else {
      setActiveSection("") // Clear active section if not on homepage
    }
  }, [pathname])

  // Fetch user language from Supabase on component mount
  useEffect(() => {
    const fetchUserLanguage = async () => {
      if (isLoaded && user && supabase) {
        const { data, error } = await supabase
          .from('user_profiles')
          .select('language')
          .eq('user_id', user.id)
          .single();

        if (error && error.code !== 'PGRST116') {
          console.error('Error fetching user language in navigation:', error);
        } else if (data) {
          const lang = data.language || 'en';
          i18n.changeLanguage(lang);
          setCurrentLanguage(lang);
        }
      }
    };
    if (isLoaded) {
      fetchUserLanguage();
    }
  }, [user, supabase, isLoaded]);

  const handleLanguageChange = async (lang: string) => {
    i18n.changeLanguage(lang);
    setCurrentLanguage(lang);

    if (user && supabase) {
      const clerkUserId = user.id;
      const { error } = await supabase
        .from('user_profiles')
        .upsert({ user_id: clerkUserId, language: lang }, { onConflict: 'user_id' });

      if (error) {
        console.error('Error saving language preference:', error);
      } else {
        console.log('Language preference saved to Supabase:', lang);
      }
    }
  };

  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="container mx-auto max-w-6xl px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/icon_DPS.png"
              alt="DiabetesPlateScan Logo"
              width={32}
              height={32}
              className="rounded-lg"
            />
            <span className="font-bold text-xl text-gray-900">DiabetesPlateScan</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {/* Always show navigation links, don't wait for Clerk to load */}
            <>
              {/* Conditional rendering for main navigation links (only on homepage when not logged in) */}
              <SignedOut>
                {pathname === "/" && (
                  <>
                    {mainNavigation.map((item) => (
                      <a
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "px-3 py-2 rounded-md text-sm font-medium transition-colors",
                          activeSection === item.href.substring(1)
                            ? "bg-blue-100 text-blue-700 font-semibold"
                            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50",
                        )}
                        onClick={(e) => {
                          e.preventDefault();
                          document.getElementById(item.href.substring(1))?.scrollIntoView({ behavior: 'smooth' });
                        }}
                      >
                        {t(`mainNavigation.${item.name.replace(/\s/g, '')}`)}
                      </a>
                    ))}
                  </>
                )}
              </SignedOut>

              {/* Conditional rendering for logged-in navigation links (only when logged in) */}
              <SignedIn>
                <>
                  {loggedInNavigation.map((item) => {
                    const Icon = item.icon
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                          pathname === item.href
                            ? "bg-blue-100 text-blue-700"
                            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50",
                        )}
                      >
                        <Icon className="h-4 w-4" />
                        <span>{t(`loggedInNavigation.${item.name}`)}</span>
                      </Link>
                    )
                  })}
                  {/* Language Selector */}
                  <Select onValueChange={handleLanguageChange} value={currentLanguage}>
                    <SelectTrigger className="">
                      <Languages className="h-4 w-4" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">{t('languages.en')}</SelectItem>
                      <SelectItem value="zh">{t('languages.zh')}</SelectItem>
                    </SelectContent>
                  </Select>
                </>
              </SignedIn>

              {/* Login/Logout button */}
              <SignedOut>
                {/* Language Selector */}
                <Select onValueChange={handleLanguageChange} value={currentLanguage}>
                  <SelectTrigger className="w-[100px]">
                    <Languages className="h-4 w-4" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">{t('languages.en')}</SelectItem>
                    <SelectItem value="zh">{t('languages.zh')}</SelectItem>
                  </SelectContent>
                </Select>
                <SignInButton mode="modal">
                  <Button variant="default" size="sm" className="bg-blue-600 text-white hover:bg-blue-700">{t('auth.login')}</Button>
                </SignInButton>
              </SignedOut>
              <SignedIn>
                {isLoaded && user && <UserButton afterSignOutUrl="/"/>}
              </SignedIn>
            </>
          </div>

          {/* Mobile menu items */}
          <div className="md:hidden flex items-center space-x-2">
            <SignedIn>
              {/* Mobile Language Selector for SignedIn users */}
              <Select onValueChange={handleLanguageChange} value={currentLanguage}>
                <SelectTrigger className="min-w-[60px] max-w-[80px]">
                  <Languages className="h-4 w-4" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">{t('languages.en')}</SelectItem>
                  <SelectItem value="zh">{t('languages.zh')}</SelectItem>
                </SelectContent>
              </Select>
              {isLoaded && user && <UserButton afterSignOutUrl="/"/>}
            </SignedIn>
            <SignedOut>
              {/* Mobile Language Selector for SignedOut users */}
              <Select onValueChange={handleLanguageChange} value={currentLanguage}>
                <SelectTrigger className="min-w-[60px] max-w-[80px]">
                  <Languages className="h-4 w-4" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">{t('languages.en')}</SelectItem>
                  <SelectItem value="zh">{t('languages.zh')}</SelectItem>
                </SelectContent>
              </Select>
              <SignInButton mode="modal">
                <Button variant="default" size="sm" className="bg-blue-600 text-white hover:bg-blue-700">{t('auth.login')}</Button>
              </SignInButton>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </SignedOut>
          </div>
        </div>
        <SignedIn>
          <div className="md:hidden flex justify-center items-center space-x-4 py-2 border-t">
            {loggedInNavigation.map((item) => {
              const Icon = item.icon
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors",
                    pathname === item.href
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50",
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{t(`loggedInNavigation.${item.name}`)}</span>
                </Link>
              )
            })}
          </div>
        </SignedIn>

        {/* Mobile Navigation Links for SignedOut */}
        <SignedOut>
          <div
            className={cn(
              "md:hidden py-4 border-t",
              mobileMenuOpen ? "block" : "hidden"
            )}
          >
            <div className="space-y-2">
              {pathname === "/" && (
                  <div className="flex flex-wrap gap-x-4 gap-y-2 justify-center">
                    {mainNavigation.map((item) => (
                      <a
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "px-3 py-2 rounded-md text-base font-medium transition-colors",
                          activeSection === item.href.substring(1)
                            ? "bg-blue-100 text-blue-700 font-semibold"
                            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50",
                        )}
                        onClick={(e) => {
                          e.preventDefault();
                          document.getElementById(item.href.substring(1))?.scrollIntoView({ behavior: 'smooth' });
                          setMobileMenuOpen(false); // Close menu on link click
                        }}
                      >
                        {t(`mainNavigation.${item.name.replace(/\s/g, '')}`)}
                      </a>
                    ))}
                  </div>
                )}
            </div>
          </div>
        </SignedOut>
      </div>
    </nav>
  )
}
