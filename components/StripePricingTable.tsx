"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getStripeJs } from '@/lib/stripe';
import { useSubscription } from '@/hooks/useSubscription';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

export function StripePricingTable() {
  const { t } = useTranslation();
  const { subscription, loading } = useSubscription();
  const [isYearly, setIsYearly] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);

  const handleSubscribe = async (plan: string) => {
    setLoadingPlan(plan);
    try {
      // Handle trial activation separately
      if (plan === 'trial') {
        const response = await fetch('/api/subscription/activate-trial', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          // Refresh the page to show updated trial status
          window.location.reload();
        } else {
          console.error('Failed to activate trial');
        }
        return;
      }

      console.log('Sending checkout request for plan:', plan);
      const response = await fetch('/api/stripe/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Checkout session error response:', response.status, errorText);
        try {
          const errorData = JSON.parse(errorText);
          console.error('Parsed error data:', errorData);
          alert(`Error: ${errorData.error || 'Failed to create checkout session'}`);
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
          alert(`Error: Failed to create checkout session (${response.status})`);
        }
        return;
      }

      const responseData = await response.json();
      console.log('Checkout response:', responseData);
      const { sessionId } = responseData;

      if (!sessionId) {
        console.error('No session ID received from server');
        return;
      }

      const stripe = await getStripeJs();

      if (stripe) {
        await stripe.redirectToCheckout({ sessionId });
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
    } finally {
      setLoadingPlan(null);
    }
  };

  const handleManageSubscription = async () => {
    setLoadingPlan('manage');
    try {
      const response = await fetch('/api/stripe/customer-portal', {
        method: 'POST',
      });

      const { url } = await response.json();
      window.location.href = url;
    } catch (error) {
      console.error('Error opening customer portal:', error);
    } finally {
      setLoadingPlan(null);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Billing Toggle */}
      <div className="flex justify-center">
        <ToggleGroup
          type="single"
          value={isYearly ? "yearly" : "weekly"}
          onValueChange={(value) => setIsYearly(value === "yearly")}
        >
          <ToggleGroupItem value="weekly">Weekly</ToggleGroupItem>
          <ToggleGroupItem value="yearly">
            Yearly
            <Badge variant="secondary" className="ml-2">Save 77%</Badge>
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {/* Free Trial */}
        <Card className={`${subscription.isTrialActive ? 'border-blue-500 ring-2 ring-blue-200' : ''}`}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              {t('homePage.pricing.freeTrialPlan.title')}
              {subscription.isTrialActive && (
                <Badge variant="default" className="bg-blue-600">Current</Badge>
              )}
            </CardTitle>
            <CardDescription>{t('homePage.pricing.freeTrialPlan.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold mb-4">
              {t('homePage.pricing.freeTrialPlan.price')}
            </div>
            <ul className="space-y-2 mb-6">
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Complete Photo Analysis
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                7 Days Meal Tracking
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                1 Month Data Retention
              </li>
            </ul>
            {subscription.isTrialActive ? (
              <Button disabled className="w-full">
                {t('homePage.pricing.freeTrialPlan.activeButton')}
              </Button>
            ) : subscription.hasUsedTrial ? (
              <Button disabled className="w-full">
                {t('homePage.pricing.freeTrialPlan.usedButton')}
              </Button>
            ) : (
              <Button
                onClick={() => handleSubscribe('trial')}
                disabled={loadingPlan !== null}
                className="w-full"
                variant="outline"
              >
                {loadingPlan === 'trial' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                {t('homePage.pricing.freeTrialPlan.cta')}
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Starter Plan */}
        <Card className={`${subscription.planType === 'starter' ? 'border-blue-500 ring-2 ring-blue-200' : 'border-2 border-blue-600'}`}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-blue-600">
              {t('homePage.pricing.starterPlan.title')}
              {subscription.planType === 'starter' && (
                <Badge variant="default" className="bg-blue-600">Current</Badge>
              )}
            </CardTitle>
            <CardDescription>{t('homePage.pricing.starterPlan.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold mb-2">
              {isYearly ? (
                <>
                  <span className="text-2xl text-gray-500 line-through mr-2">$3.99</span>
                  <span className="text-green-600">$1.38</span>
                </>
              ) : (
                '$3.99'
              )}
              <span className="text-lg font-normal text-gray-600">/week</span>
            </div>
            <p className="text-gray-600 mb-4">
              {isYearly ? '$71.88/year' : 'Billed weekly'}
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                200 Instant Photo Analysis/Mo
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Basic Meal Tracking
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Basic Personalized Recommendations
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Standard Support
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                No Commitment, Cancel Anytime
              </li>
            </ul>
            {subscription.planType === 'starter' ? (
              <Button 
                onClick={handleManageSubscription}
                disabled={loadingPlan === 'manage'}
                className="w-full"
                variant="outline"
              >
                {loadingPlan === 'manage' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Manage Subscription
              </Button>
            ) : (
              <Button
                onClick={() => handleSubscribe(isYearly ? 'starter_yearly' : 'starter_weekly')}
                disabled={loadingPlan !== null}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                {loadingPlan === (isYearly ? 'starter_yearly' : 'starter_weekly') ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Crown className="h-4 w-4 mr-2" />
                )}
                {t('homePage.pricing.starterPlan.cta')}
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Unlimited Plan */}
        <Card className={`${subscription.planType === 'unlimited' ? 'border-blue-500 ring-2 ring-blue-200' : ''}`}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              {t('homePage.pricing.unlimitedPlan.title')}
              {subscription.planType === 'unlimited' && (
                <Badge variant="default" className="bg-blue-600">Current</Badge>
              )}
            </CardTitle>
            <CardDescription>{t('homePage.pricing.unlimitedPlan.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold mb-2">
              {isYearly ? (
                <>
                  <span className="text-2xl text-gray-500 line-through mr-2">$5.99</span>
                  <span className="text-green-600">$1.61</span>
                </>
              ) : (
                '$5.99'
              )}
              <span className="text-lg font-normal text-gray-600">/week</span>
            </div>
            <p className="text-gray-600 mb-4">
              {isYearly ? '$83.88/year' : 'Billed weekly'}
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Unlimited Instant Photo Analysis
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Comprehensive Personalized Recommendations
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                365 Days Meal Tracking
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                GI Index Monitoring
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Priority Support
              </li>
              <li className="flex items-center">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                No Commitment, Cancel Anytime
              </li>
            </ul>
            {subscription.planType === 'unlimited' ? (
              <Button 
                onClick={handleManageSubscription}
                disabled={loadingPlan === 'manage'}
                className="w-full"
                variant="outline"
              >
                {loadingPlan === 'manage' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Manage Subscription
              </Button>
            ) : (
              <Button
                onClick={() => handleSubscribe(isYearly ? 'unlimited_yearly' : 'unlimited_weekly')}
                disabled={loadingPlan !== null}
                className="w-full"
                variant="outline"
              >
                {loadingPlan === (isYearly ? 'unlimited_yearly' : 'unlimited_weekly') ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Crown className="h-4 w-4 mr-2" />
                )}
                {t('homePage.pricing.unlimitedPlan.cta')}
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
