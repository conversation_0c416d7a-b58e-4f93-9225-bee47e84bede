"use client";

import React, { useEffect } from "react";
import { I18nextProvider } from "react-i18next";
import i18n, { createI18nInstance } from "@/lib/i18n";

interface I18nProviderProps {
  children: React.ReactNode;
  language?: string;
}

export function I18nProvider({ children, language }: I18nProviderProps) {
  // If language is provided, create a new i18n instance (SSR or SSG)
  // On the client, use the singleton and set language if needed
  const isSSR = typeof window === "undefined";
  const i18nInstance = isSSR && language ? createI18nInstance(language) : i18n;

  useEffect(() => {
    if (!isSSR && language && i18n.language !== language) {
      i18n.changeLanguage(language);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [language]);

  return (
    <I18nextProvider i18n={i18nInstance}>
      {children}
    </I18nextProvider>
  );
}
