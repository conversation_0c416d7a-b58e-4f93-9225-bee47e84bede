import Link from "next/link"
import { useTranslation } from "react-i18next"
import i18n from "@/lib/i18n"

export default function Footer() {
  const { t } = useTranslation()
  return (
    <footer className="bg-gray-100 py-6 px-4 border-t">
      <div className="container mx-auto max-w-6xl flex flex-col md:flex-row items-center justify-between text-sm text-gray-600">
        <p className="mb-2 md:mb-0">© 2025 DiabetesPlateScan. All rights reserved.</p>
        <nav className="space-x-4">
          <Link href="/privacy-policy" className="hover:text-gray-900">
            Privacy Policy
          </Link>
          <Link href="/terms-of-service" className="hover:text-gray-900">
            Terms of Service
          </Link>
        </nav>
      </div>
      <div className="container mx-auto max-w-6xl text-center text-xs text-gray-500 mt-4">
        <p>{t("disclaimerMedicalAdvice")}</p>
      </div>
    </footer>
  )
}
