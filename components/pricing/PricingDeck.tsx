'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';
import { PRICING_CONFIG, PricingPlan, calculateYearlySavings, getMonthlyEquivalent } from '@/lib/pricing';
import { useTranslation } from 'react-i18next';

interface PricingDeckProps {
  showForLoggedInUser?: boolean;
  currentPlanId?: string;
  onPlanSelect?: (priceId: string, planName: string) => void;
  className?: string;
}

function PricingDeck({
  showForLoggedInUser = false,
  currentPlanId,
  onPlanSelect,
  className
}: PricingDeckProps) {
  const { t } = useTranslation();
  const handlePlanSelect = (priceId: string, planName: string) => {
    if (onPlanSelect) {
      onPlanSelect(priceId, planName);
    } else {
      // Default behavior - redirect to Stripe checkout
      window.location.href = `/api/stripe/checkout?priceId=${priceId}`;
    }
  };

  return (
    <div className={cn("w-full max-w-6xl mx-auto px-4", className)}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {PRICING_CONFIG.plans.map((plan) => (
          <Card 
            key={plan.id} 
            className={cn(
              "relative transition-all duration-300 hover:shadow-lg",
              plan.popular && "border-primary shadow-lg scale-105",
              currentPlanId === plan.id && "ring-2 ring-primary"
            )}
          >
            {plan.popular && (
              <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-600 to-blue-600">
                <Sparkles className="w-3 h-3 mr-1" />
                {t('homePage.pricingDeck.mostPopular')}
              </Badge>
            )}
            
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-xl font-bold">
                {plan.id === 'free' ? t('homePage.pricing.freeTrialPlan.title') :
                 plan.id === 'starter' ? t('homePage.pricing.starterPlan.title') :
                 plan.id === 'unlimited' ? t('homePage.pricing.unlimitedPlan.title') :
                 plan.name}
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {plan.id === 'free' ? t('homePage.pricing.freeTrialPlan.description') :
                 plan.id === 'starter' ? t('homePage.pricing.starterPlan.description') :
                 plan.id === 'unlimited' ? t('homePage.pricing.unlimitedPlan.description') :
                 plan.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* Pricing Section */}
              <div className="space-y-4">
                {plan.id === 'free' ? (
                  <div className="text-center">
                    <div className="text-4xl font-bold">$0</div>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="text-4xl font-bold">${plan.weeklyPrice}</div>
                    <div className="text-sm text-muted-foreground">/week</div>
                    <div className="text-sm text-muted-foreground">{t('homePage.pricingDeck.billedWeekly')}</div>
                  </div>
                )}
              </div>
              
              {/* Features */}
              <div className="space-y-3">
                {plan.id === 'free' && [
                  t('homePage.pricing.freeTrialPlan.feature1'),
                  t('homePage.pricing.freeTrialPlan.feature2'),
                  t('homePage.pricing.freeTrialPlan.feature3')
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">{feature}</span>
                  </div>
                ))}
                {plan.id === 'starter' && [
                  t('homePage.pricing.starterPlan.feature1'),
                  t('homePage.pricing.starterPlan.feature2'),
                  t('homePage.pricing.starterPlan.feature3'),
                  t('homePage.pricing.starterPlan.feature4'),
                  t('homePage.pricing.starterPlan.feature5')
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">{feature}</span>
                  </div>
                ))}
                {plan.id === 'unlimited' && [
                  t('homePage.pricing.unlimitedPlan.feature1'),
                  t('homePage.pricing.unlimitedPlan.feature2'),
                  t('homePage.pricing.unlimitedPlan.feature3'),
                  t('homePage.pricing.unlimitedPlan.feature4'),
                  t('homePage.pricing.unlimitedPlan.feature5'),
                  t('homePage.pricing.unlimitedPlan.feature6')
                ].map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Pricing Buttons - Moved to bottom */}
              <div className="space-y-2 pt-4">
                {plan.id === 'free' ? (
                  <Button
                    onClick={() => handlePlanSelect('', plan.name)}
                    variant="outline"
                    className="w-full"
                    disabled={currentPlanId === plan.id}
                  >
                    {currentPlanId === plan.id ? t('homePage.pricingDeck.currentPlan') : t('homePage.pricing.freeTrialPlan.button')}
                  </Button>
                ) : (
                  <>
                    {/* Yearly Button */}
                    <Button
                      onClick={() => handlePlanSelect(plan.yearlyPriceId, `${plan.name} Yearly`)}
                      className="w-full h-auto p-3 bg-blue-600 hover:bg-blue-700 text-white"
                      disabled={currentPlanId === plan.id}
                    >
                      <div className="w-full">
                        {/* Mobile Layout - Stacked */}
                        <div className="block sm:hidden">
                          <div className="flex justify-between items-center mb-1">
                            <div className="font-semibold text-sm">{t('homePage.pricingDeck.annual')}</div>
                            <div className="text-sm font-bold">
                              ${(plan.yearlyPrice / 52).toFixed(2)}/wk
                            </div>
                          </div>
                          <div className="flex justify-between items-center text-xs">
                            <div className="opacity-90">${plan.yearlyPrice}/yr</div>
                            <div className="bg-green-100 text-green-700 px-1.5 py-0.5 rounded text-xs">
                              {t('homePage.pricingDeck.save', { percent: Math.round(((plan.weeklyPrice * 52 - plan.yearlyPrice) / (plan.weeklyPrice * 52)) * 100) })}
                            </div>
                          </div>
                        </div>

                        {/* Desktop Layout - Side by Side */}
                        <div className="hidden sm:flex justify-between items-center">
                          <div className="text-left">
                            <div className="font-semibold text-base">{t('homePage.pricingDeck.annual')}</div>
                            <div className="text-xs opacity-90">
                              ${plan.yearlyPrice} {t('homePage.pricingDeck.perYear')}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold">
                              ${(plan.yearlyPrice / 52).toFixed(2)}/wk
                            </div>
                            <div className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                              {t('homePage.pricingDeck.save', { percent: Math.round(((plan.weeklyPrice * 52 - plan.yearlyPrice) / (plan.weeklyPrice * 52)) * 100) })}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Button>

                    {/* Weekly Button */}
                    <Button
                      onClick={() => handlePlanSelect(plan.weeklyPriceId, `${plan.name} Weekly`)}
                      className="w-full h-auto p-3 bg-blue-600 hover:bg-blue-700 text-white"
                      disabled={currentPlanId === plan.id}
                    >
                      <div className="w-full">
                        {/* Mobile Layout - Stacked */}
                        <div className="block sm:hidden">
                          <div className="flex justify-between items-center">
                            <div className="font-semibold text-sm">{t('homePage.pricingDeck.weekly')}</div>
                            <div className="text-sm font-bold">
                              ${plan.weeklyPrice}/wk
                            </div>
                          </div>
                          <div className="text-xs opacity-90 text-left mt-1">
                            {t('homePage.pricingDeck.billedWeekly')}
                          </div>
                        </div>

                        {/* Desktop Layout - Side by Side */}
                        <div className="hidden sm:flex justify-between items-center">
                          <div className="text-left">
                            <div className="font-semibold text-base">{t('homePage.pricingDeck.weekly')}</div>
                            <div className="text-xs opacity-90">
                              {t('homePage.pricingDeck.billedWeekly')}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold">
                              ${plan.weeklyPrice}/wk
                            </div>
                          </div>
                        </div>
                      </div>
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default PricingDeck;
export { PricingDeck };
