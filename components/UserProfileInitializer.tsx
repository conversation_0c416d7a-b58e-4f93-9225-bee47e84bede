"use client";

import { useUser } from "@clerk/nextjs";
import { useClerkSupabase } from "@/lib/supabaseClient";
import { useEffect } from "react";
import { addDays } from 'date-fns';

export default function UserProfileInitializer() {
  const { user } = useUser();
  const supabase = useClerkSupabase();

  useEffect(() => {
    const setupUserProfile = async () => {
      if (user && supabase) {
        const { data: userProfile, error } = await supabase
          .from('user_profiles')
          .select('has_trial_ever_been_active, email')
          .eq('user_id', user.id)
          .single();

        if (error && error.code === 'PGRST116') { // No rows found
          if (!user.emailAddresses || user.emailAddresses.length === 0) {
            console.error('User has no email address. Cannot create profile.');
            return;
          }
          // New user, create profile and start trial
          const trialStartDate = new Date();
          const trialEndDate = addDays(trialStartDate, 7);

          const { error: insertError } = await supabase.from('user_profiles').insert({
            user_id: user.id,
            email: user.emailAddresses[0].emailAddress,
            trial_start_date: trialStartDate.toISOString(),
            trial_end_date: trialEndDate.toISOString(),
            is_trial_active: true,
            has_trial_ever_been_active: true,
          });

          if (insertError) {
            console.error('Error creating user profile and starting trial:', JSON.stringify(insertError, null, 2));
          } else {
            console.log('New user profile created and trial started.');
            // Trigger a page refresh to update subscription status
            window.location.reload();
          }
        } else if (userProfile && !userProfile.has_trial_ever_been_active) {
          // Existing user without a trial, activate trial
          const trialStartDate = new Date();
          const trialEndDate = addDays(trialStartDate, 7);

          const updateData: any = {
            trial_start_date: trialStartDate.toISOString(),
            trial_end_date: trialEndDate.toISOString(),
            is_trial_active: true,
            has_trial_ever_been_active: true,
          };

          // Add email if it's missing
          if (!userProfile.email && user.emailAddresses && user.emailAddresses.length > 0) {
            updateData.email = user.emailAddresses[0].emailAddress;
          }

          const { error: updateError } = await supabase
            .from('user_profiles')
            .update(updateData)
            .eq('user_id', user.id);

          if (updateError) {
            console.error('Error activating trial for existing user:', updateError);
          } else {
            console.log('Trial activated for existing user.');
            // Trigger a page refresh to update subscription status
            window.location.reload();
          }
        } else if (userProfile && !userProfile.email && user.emailAddresses && user.emailAddresses.length > 0) {
          // Existing user missing email, update it
          const { error: updateError } = await supabase
            .from('user_profiles')
            .update({ email: user.emailAddresses[0].emailAddress })
            .eq('user_id', user.id);

          if (updateError) {
            console.error('Error updating user email:', updateError);
          } else {
            console.log('User email updated.');
          }
        }
      }
    };

    setupUserProfile();
  }, [user, supabase]);

  return null; // This component doesn't render anything visible
}
