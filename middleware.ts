import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from 'next/server';

const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/api/update-food-analysis(.*)',
  '/api/analyze-food(.*)',
  '/api/analyze-food-stream(.*)',
  '/api/generate-celebration(.*)',
]);

export default clerkMiddleware(async (auth, req) => {
  const { userId } = await auth();
  if (isProtectedRoute(req)) {
    if (!userId) {
      // Use Clerk's redirectToSignIn method
      return (await auth()).redirectToSignIn();
    }
  }
});
 
export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)", "/dashboard(.*)"],
};
