# Stripe Billing Setup Guide

This project has been migrated from Clerk billing to Stripe billing. Follow these steps to complete the setup.

## 1. Stripe Dashboard Setup

### Create Products and Prices
1. Go to your [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Products** → **Add Product**
3. Create the following products:

#### Starter Plan
- **Name**: Starter Plan
- **Weekly Price**: $3.99/week (recurring)
- **Yearly Price**: $71.88/year (recurring) - 77% discount ($5.99/month)

#### Unlimited Plan
- **Name**: Unlimited Plan
- **Weekly Price**: $5.99/week (recurring)
- **Yearly Price**: $83.88/year (recurring) - 77% discount ($6.99/month)

### Get Price IDs
After creating the products, copy the Price IDs and update them in `lib/stripe.ts`:

```typescript
export const STRIPE_PLANS = {
  starter_weekly: 'price_1Rgc81PIVNHn8cqrcAcYaCGN',   // Starter Plan - Weekly
  starter_yearly: 'price_1Rgc8vPIVNHn8cqriwYHdXMy',   // Starter Plan - Yearly
  unlimited_weekly: 'price_1Rgc9ePIVNHn8cqrlQPacpPy', // Unlimited Plan - Weekly
  unlimited_yearly: 'price_1RgcBwPIVNHn8cqr3fshT47I', // Unlimited Plan - Yearly
} as const;
```

## 2. Webhook Setup

### Create Webhook Endpoint
1. In Stripe Dashboard, go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL to: `https://yourdomain.com/api/stripe/webhook`
4. Select these events:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

### Update Webhook Secret
Copy the webhook signing secret and update `.env.local`:
```
STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret_here
```

## 3. Environment Variables

Ensure these are set in your `.env.local`:

```env
# Stripe credentials
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51Rdo2NPIVNHn8cqr31RZH4WGd2kIrR5UpIZneI331bojHm2iYpNXVXk5VGVq5B0A3VTlfG3zr5pzad8kpUTO9ZTe00CdwavLtI
STRIPE_SECRET_KEY=sk_test_51Rdo2NPIVNHn8cqryBH2v4BkUQSKTNtW54WqKlcbjFvcvQ8sNf7YRHpVYTChVsUe8F6rGww2ZTppddy4BIf01zv0005iJMKSJw
STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret_here

# Supabase service role key (for webhooks)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.EEE8Y0-eAOBPC1TuWiATTGcj8iEgOBrdz35qtfjC0ek
```

## 4. Database Migration

The Stripe fields have been added to the `user_profiles` table:
- `stripe_customer_id`
- `stripe_subscription_id` 
- `stripe_subscription_status`
- `stripe_current_period_end`
- `stripe_plan_id`

## 5. Testing

### Test Mode
- Use Stripe test cards: https://stripe.com/docs/testing
- Test card: `4242 4242 4242 4242`

### Webhook Testing
Use Stripe CLI to test webhooks locally:
```bash
stripe listen --forward-to localhost:3000/api/stripe/webhook
```

## 6. Migration Summary

### What Changed:
1. **Billing System**: Clerk billing → Stripe billing
2. **Subscription Checks**: `has({ plan: 'starter' })` → Stripe subscription status
3. **Trial Management**: Still uses existing trial system (7 days)
4. **Pricing Components**: Clerk PricingTable → Custom StripePricingTable
5. **Access Control**: Updated to check Stripe subscription status

### Key Features:
- ✅ 7-day free trial (existing system)
- ✅ Stripe subscription management
- ✅ Customer portal for subscription management
- ✅ Webhook handling for subscription updates
- ✅ Automatic access control based on subscription status
- ✅ Multilingual support maintained

### Files Modified:
- `lib/stripe.ts` - Stripe configuration
- `hooks/useSubscription.ts` - Subscription management hook
- `components/StripePricingTable.tsx` - New pricing component
- `app/api/stripe/` - Stripe API endpoints
- `app/api/user-plan/route.ts` - Updated to use Stripe
- `app/api/analyze-food/route.ts` - Updated access control
- `app/dashboard/analyze/page.tsx` - Updated UI logic
- `app/dashboard/profile/page.tsx` - Updated pricing component
- `app/pricing/page.tsx` - Updated pricing component

## 7. Next Steps

1. Update Price IDs in `lib/stripe.ts`
2. Set up webhook endpoint in production
3. Update webhook secret in environment variables
4. Test subscription flow end-to-end
5. Monitor webhook delivery in Stripe Dashboard
