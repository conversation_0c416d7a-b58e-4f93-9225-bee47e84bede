import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useClerkSupabase } from '@/lib/supabaseClient';

export interface SubscriptionData {
  isActive: boolean;
  isTrialActive: boolean;
  trialEndDate: Date | null;
  hasUnlimitedScans: boolean;
  planType: 'trial' | 'starter' | 'unlimited' | 'none';
  subscriptionStatus: string | null;
  currentPeriodEnd: Date | null;
  hasUsedTrial: boolean;
}

export function useSubscription() {
  const { user } = useUser();
  const supabase = useClerkSupabase();
  const [subscription, setSubscription] = useState<SubscriptionData>({
    isActive: false,
    isTrialActive: false,
    trialEndDate: null,
    hasUnlimitedScans: false,
    planType: 'none',
    subscriptionStatus: null,
    currentPeriodEnd: null,
    hasUsedTrial: false,
  });
  const [loading, setLoading] = useState(true);

  const fetchSubscriptionData = async () => {
    if (!user || !supabase) {
      setLoading(false);
      return;
    }

    try {
      const { data: userProfile, error } = await supabase
        .from('user_profiles')
        .select(`
          trial_start_date,
          trial_end_date,
          is_trial_active,
          has_trial_ever_been_active,
          stripe_customer_id,
          stripe_subscription_id,
          stripe_subscription_status,
          stripe_current_period_end,
          stripe_plan_id
        `)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching subscription data:', error);
        setLoading(false);
        return;
      }

      const now = new Date();
      const trialEndDate = userProfile?.trial_end_date ? new Date(userProfile.trial_end_date) : null;
      const currentPeriodEnd = userProfile?.stripe_current_period_end ? new Date(userProfile.stripe_current_period_end) : null;

      // Check if trial is active
      const isTrialActive = userProfile?.is_trial_active && trialEndDate && now < trialEndDate;

      // Check if Stripe subscription is active
      // If currentPeriodEnd is null but status is active, consider it active (fallback for webhook issues)
      const isStripeActive = userProfile?.stripe_subscription_status === 'active' &&
                            (currentPeriodEnd ? now < currentPeriodEnd : true);

      // Determine plan type
      let planType: SubscriptionData['planType'] = 'none';
      if (isTrialActive) {
        planType = 'trial';
      } else if (isStripeActive) {
        // Determine plan based on exact price ID match (handles both weekly and yearly)
        if (userProfile.stripe_plan_id === 'price_1Rgc81PIVNHn8cqrcAcYaCGN' || // starter weekly
            userProfile.stripe_plan_id === 'price_1Rgc8vPIVNHn8cqriwYHdXMy') { // starter yearly
          planType = 'starter';
        } else if (userProfile.stripe_plan_id === 'price_1Rgc9ePIVNHn8cqrlQPacpPy' || // unlimited weekly
                   userProfile.stripe_plan_id === 'price_1RgcBwPIVNHn8cqr3fshT47I') { // unlimited yearly
          planType = 'unlimited';
        }
      }

      const hasUnlimitedScans = isTrialActive || isStripeActive;

      setSubscription({
        isActive: isStripeActive || false,
        isTrialActive: isTrialActive || false,
        trialEndDate,
        hasUnlimitedScans,
        planType,
        subscriptionStatus: userProfile?.stripe_subscription_status || null,
        currentPeriodEnd,
        hasUsedTrial: userProfile?.has_trial_ever_been_active || false,
      });
    } catch (error) {
      console.error('Error in fetchSubscriptionData:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubscriptionData();
  }, [user, supabase]);

  const refetch = () => {
    setLoading(true);
    fetchSubscriptionData();
  };

  return { subscription, loading, refetch };
}
