"use client";

import PricingDeck from '@/components/pricing/PricingDeck';
import { useTranslation } from 'react-i18next';
import { useSubscription } from '@/hooks/useSubscription';

export default function PricingPage() {
  const { t } = useTranslation();
  const { subscription } = useSubscription();

  return (
    <div className="max-w-7xl mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">{t('homePage.pricing.title')}</h1>
        <p className="text-xl text-gray-600">{t('homePage.pricing.description')}</p>
      </div>
      <PricingDeck
        showForLoggedInUser={true}
        currentPlanId={subscription.planType}
      />
    </div>
  );
}
