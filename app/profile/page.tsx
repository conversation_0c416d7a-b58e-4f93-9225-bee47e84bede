"use client"

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>, Shield, Heart } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"

export default function ProfilePage() {
  const [profile, setProfile] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    diabetesType: "type2",
    diagnosisYear: "2020",
    medications: "Metformin, Insulin",
    allergies: "None",
    dietaryPreferences: "Low-carb",
    activityLevel: "moderate",
    notifications: {
      mealReminders: true,
      analysisResults: true,
      weeklyReports: false,
      communityUpdates: true,
    },
  })

  const handleSave = () => {
    // Save profile logic here
    console.log("Profile saved:", profile)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto max-w-4xl px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Profile Settings</h1>
          <p className="text-gray-600">Manage your personal information and preferences</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Navigation */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-6">
                <nav className="space-y-2">
                  <a href="#personal" className="flex items-center space-x-3 p-3 rounded-lg bg-blue-50 text-blue-700">
                    <User className="h-5 w-5" />
                    <span>Personal Info</span>
                  </a>
                  <a href="#health" className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <Heart className="h-5 w-5" />
                    <span>Health Profile</span>
                  </a>
                  <a href="#notifications" className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <Bell className="h-5 w-5" />
                    <span>Notifications</span>
                  </a>
                  <a href="#privacy" className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <Shield className="h-5 w-5" />
                    <span>Privacy</span>
                  </a>
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Personal Information */}
            <Card id="personal">
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
                <CardDescription>Update your basic profile information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      value={profile.name}
                      onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profile.email}
                      onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Health Profile */}
            <Card id="health">
              <CardHeader>
                <CardTitle>Health Profile</CardTitle>
                <CardDescription>Help us provide personalized recommendations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="diabetesType">Diabetes Type</Label>
                    <Select
                      value={profile.diabetesType}
                      onValueChange={(value) => setProfile({ ...profile, diabetesType: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="type1">Type 1</SelectItem>
                        <SelectItem value="type2">Type 2</SelectItem>
                        <SelectItem value="gestational">Gestational</SelectItem>
                        <SelectItem value="prediabetes">Pre-diabetes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="diagnosisYear">Year of Diagnosis</Label>
                    <Input
                      id="diagnosisYear"
                      value={profile.diagnosisYear}
                      onChange={(e) => setProfile({ ...profile, diagnosisYear: e.target.value })}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="medications">Current Medications</Label>
                  <Textarea
                    id="medications"
                    value={profile.medications}
                    onChange={(e) => setProfile({ ...profile, medications: e.target.value })}
                    placeholder="List your current diabetes medications..."
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="allergies">Food Allergies</Label>
                    <Input
                      id="allergies"
                      value={profile.allergies}
                      onChange={(e) => setProfile({ ...profile, allergies: e.target.value })}
                      placeholder="e.g., Nuts, Shellfish"
                    />
                  </div>
                  <div>
                    <Label htmlFor="activityLevel">Activity Level</Label>
                    <Select
                      value={profile.activityLevel}
                      onValueChange={(value) => setProfile({ ...profile, activityLevel: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sedentary">Sedentary</SelectItem>
                        <SelectItem value="light">Light Activity</SelectItem>
                        <SelectItem value="moderate">Moderate Activity</SelectItem>
                        <SelectItem value="active">Very Active</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Notifications */}
            <Card id="notifications">
              <CardHeader>
                <CardTitle>Notification Preferences</CardTitle>
                <CardDescription>Choose what notifications you'd like to receive</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="mealReminders">Meal Analysis Reminders</Label>
                    <p className="text-sm text-gray-500">Get reminded to analyze your meals</p>
                  </div>
                  <Switch
                    id="mealReminders"
                    checked={profile.notifications.mealReminders}
                    onCheckedChange={(checked) =>
                      setProfile({
                        ...profile,
                        notifications: { ...profile.notifications, mealReminders: checked },
                      })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="analysisResults">Analysis Results</Label>
                    <p className="text-sm text-gray-500">Receive notifications when analysis is complete</p>
                  </div>
                  <Switch
                    id="analysisResults"
                    checked={profile.notifications.analysisResults}
                    onCheckedChange={(checked) =>
                      setProfile({
                        ...profile,
                        notifications: { ...profile.notifications, analysisResults: checked },
                      })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="weeklyReports">Weekly Progress Reports</Label>
                    <p className="text-sm text-gray-500">Get weekly summaries of your progress</p>
                  </div>
                  <Switch
                    id="weeklyReports"
                    checked={profile.notifications.weeklyReports}
                    onCheckedChange={(checked) =>
                      setProfile({
                        ...profile,
                        notifications: { ...profile.notifications, weeklyReports: checked },
                      })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="communityUpdates">Community Updates</Label>
                    <p className="text-sm text-gray-500">Stay updated with community discussions</p>
                  </div>
                  <Switch
                    id="communityUpdates"
                    checked={profile.notifications.communityUpdates}
                    onCheckedChange={(checked) =>
                      setProfile({
                        ...profile,
                        notifications: { ...profile.notifications, communityUpdates: checked },
                      })
                    }
                  />
                </div>
              </CardContent>
            </Card>

            {/* Save Button */}
            <div className="flex justify-end">
              <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700">
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
