import { NextResponse, NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export async function GET(request: NextRequest) {
  try {
    const { userId, getToken } = await auth();

    if (!userId) {
      return NextResponse.json({ hasUnlimitedScans: false, error: 'Unauthorized' }, { status: 401 });
    }

    const token = await getToken({ template: 'supabase' });
    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: { Authorization: `Bearer ${token}` },
      },
    });

    const { data: userProfile, error } = await supabase
      .from('user_profiles')
      .select(`
        trial_start_date,
        trial_end_date,
        is_trial_active,
        stripe_subscription_status,
        stripe_current_period_end
      `)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      return NextResponse.json({ hasUnlimitedScans: false, error: 'Failed to fetch user profile' }, { status: 500 });
    }

    const now = new Date();
    const trialEndDate = userProfile?.trial_end_date ? new Date(userProfile.trial_end_date) : null;
    const currentPeriodEnd = userProfile?.stripe_current_period_end ? new Date(userProfile.stripe_current_period_end) : null;

    // Check if trial is active
    const isTrialActive = userProfile?.is_trial_active && trialEndDate && now < trialEndDate;

    // Check if Stripe subscription is active
    const isStripeActive = userProfile?.stripe_subscription_status === 'active' &&
                          currentPeriodEnd && now < currentPeriodEnd;

    const hasUnlimitedScans = isTrialActive || isStripeActive;

    return NextResponse.json({
      hasUnlimitedScans,
      isTrialActive,
      isStripeActive,
      trialEndDate: trialEndDate?.toISOString(),
      currentPeriodEnd: currentPeriodEnd?.toISOString()
    });
  } catch (error) {
    console.error('Error in user-plan API:', error);
    return NextResponse.json({ hasUnlimitedScans: false, error: 'Failed to check plan status' }, { status: 500 });
  }
}
