import { NextResponse, NextRequest } from 'next/server';
import { auth, getAuth, currentUser } from '@clerk/nextjs/server';
import { createClient } from '@supabase/supabase-js';
import { addDays } from 'date-fns';

export async function POST(request: NextRequest) {
  try {
    const { imageUrl, targetLanguage = 'en' } = await request.json(); // Destructure targetLanguage with default
    const { userId, has } = await auth();
    const { getToken } = getAuth(request);
    const token = await getToken({ template: 'supabase' });
    
    if (!token) {
      return NextResponse.json({ error: 'Not authenticated with Supabase' }, { status: 401 });
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }

    // Check image size (base64 images can be very large on mobile)
    const imageSizeInBytes = imageUrl.length * 0.75; // Approximate size after base64 decoding
    const maxSizeInMB = 10;
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;

    if (imageSizeInBytes > maxSizeInBytes) {
      return NextResponse.json({
        error: `Image too large. Maximum size is ${maxSizeInMB}MB. Your image is approximately ${(imageSizeInBytes / (1024 * 1024)).toFixed(1)}MB.`
      }, { status: 413 });
    }

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json({ error: 'Supabase credentials are not set' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: { Authorization: `Bearer ${token}` },
      },
    });

    let { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select(`
        trial_start_date,
        trial_end_date,
        is_trial_active,
        has_trial_ever_been_active,
        stripe_subscription_status,
        stripe_current_period_end
      `)
      .eq('user_id', userId)
      .single();

    if (profileError && profileError.code === 'PGRST116') { // No rows found
        // Create new user profile with trial data
        const trialStartDate = new Date();
        const trialEndDate = addDays(trialStartDate, 7);

        const user = await currentUser(); // Get the full user object
        const userEmail = user?.emailAddresses[0]?.emailAddress;

        const { data: newProfile, error: insertError } = await supabase.from('user_profiles').insert({
            user_id: userId,
            email: userEmail,
            trial_start_date: trialStartDate.toISOString(),
            trial_end_date: trialEndDate.toISOString(),
            is_trial_active: true,
            has_trial_ever_been_active: true,
        }).select('trial_start_date, trial_end_date, is_trial_active, has_trial_ever_been_active').single();

        if (insertError) {
            console.error('Error inserting new user profile:', insertError);
            return NextResponse.json({ error: 'Failed to create user profile' }, { status: 500 });
        }
        userProfile = newProfile; // Use the newly created profile
    } else if (profileError) {
        console.error('Error fetching user profile:', profileError);
        return NextResponse.json({ error: 'Failed to fetch user profile' }, { status: 500 });
    }

    // Check subscription status using Stripe data
    const now = new Date();
    const trialEndDate = userProfile?.trial_end_date ? new Date(userProfile.trial_end_date) : null;
    const currentPeriodEnd = userProfile?.stripe_current_period_end ? new Date(userProfile.stripe_current_period_end) : null;

    // Check if trial is active
    const isTrialActive = userProfile?.is_trial_active && trialEndDate && now < trialEndDate;

    // Check if Stripe subscription is active
    // If currentPeriodEnd is null but status is active, consider it active (fallback for webhook issues)
    const isStripeActive = userProfile?.stripe_subscription_status === 'active' &&
                          (currentPeriodEnd ? now < currentPeriodEnd : true);

    const hasUnlimitedScans = isTrialActive || isStripeActive;

    // Debug log for subscription status
    console.log('API analyze-food:', {
      userId,
      hasUnlimitedScans,
      isTrialActive,
      isStripeActive,
      trialEndDate: userProfile?.trial_end_date,
      stripeStatus: userProfile?.stripe_subscription_status,
      currentPeriodEnd: userProfile?.stripe_current_period_end,
      currentPeriodEndParsed: currentPeriodEnd?.toISOString(),
      usingFallbackForActiveStripe: userProfile?.stripe_subscription_status === 'active' && !currentPeriodEnd,
      now: now.toISOString(),
    });

    if (!hasUnlimitedScans) {
      return NextResponse.json({
        error: 'Trial expired or no active plan. Please upgrade for unlimited scans.',
        debug: {
          hasUnlimitedScans,
          isTrialActive,
          isStripeActive,
          trialEndDate: userProfile?.trial_end_date,
          stripeStatus: userProfile?.stripe_subscription_status,
          now: now.toISOString(),
        }
      }, { status: 403 });
    }

    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ error: 'OPENROUTER_API_KEY is not set' }, { status: 500 });
    }

    const languageInstruction = targetLanguage === 'en' ? '' : ` Provide all text content in ${targetLanguage === 'zh' ? 'Chinese' : targetLanguage}.`;

    let response;
    try {
      response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://diabetesplatescan.com',
        'X-Title': 'DiabetesPlateScan',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash-preview-05-20',

        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Analyze the food in this image for a diabetic patient.${languageInstruction}

IMPORTANT: Focus ONLY on the food items in the image. If you see utensils (spoons, forks, knives, plates, bowls, etc.) alongside food, ignore the utensils and analyze only the food. Do not return an error for images containing both food and utensils.

Provide the following information in a JSON format:
                {
                  "isFood": "boolean",
                  "foodName": "string",
                  "confidence": "number (percentage, e.g., 95)",
                  "suitability": "good" | "moderate" | "caution",
                  "glycemicIndex": {"min": "number", "max": "number"},
                  "carbs": {"min": "number (grams)", "max": "number (grams)"},
                  "sugar": {"min": "number (grams)", "max": "number (grams)"},
                  "fiber": {"min": "number (grams)", "max": "number (grams)"},
                  "protein": {"min": "number (grams)", "max": "number (grams)"},
                  "recommendations": ["string"],
                  "portionAdvice": "string",
                  "individualItems": [
                    {
                      "name": "string",
                      "contribution": "string",
                      "concernsForDiabetes": "string",
                      "suitability": "good" | "moderate" | "caution"
                    }
                  ]
                }
                If the image contains ONLY utensils or kitchen items with NO food visible, then set "isFood" to false.
                If the image contains food (even with utensils present), set "isFood" to true and analyze the food.

                CRITICAL INSTRUCTIONS:
                1. **Nutritional Ranges**: Provide realistic ranges for glycemicIndex, carbs, protein, fiber, and sugar based on typical serving sizes and food variations. The ranges should account for preparation methods, portion sizes, and natural variations in ingredients.

                2. **Precise Recommendations**: Provide 3-4 specific, actionable recommendations that include:
                   - Exact portion size suggestions (e.g., "Limit to 1/2 cup serving")
                   - Specific pairing suggestions (e.g., "Pair with 2-3 oz lean protein")
                   - Timing recommendations (e.g., "Best consumed before 2 PM")
                   - Blood sugar monitoring advice (e.g., "Check blood sugar 2 hours after eating")
                   - Preparation modifications (e.g., "Choose steamed over fried preparation")

                3. **Accuracy**: Leverage your nutritional database for precise data. Ranges should be realistic - typically 15-25% variation from the median value for most foods.

                For portionAdvice, provide specific serving size guidance with visual references. For individualItems, provide detailed breakdown for each food component identified in the image. Example:
                {
                  "isFood": true,
                  "foodName": "Radish Cake with Eggs",
                  "confidence": 94,
                  "suitability": "moderate",
                  "glycemicIndex": {"min": 50, "max": 60},
                  "carbs": {"min": 40, "max": 50},
                  "sugar": {"min": 4, "max": 6},
                  "fiber": {"min": 4, "max": 6},
                  "protein": {"min": 18, "max": 22},
                  "recommendations": [
                    "Limit portion to 1/2 cup (about 100g) to manage carb intake",
                    "Pair with 2-3 oz lean protein and 1 cup non-starchy vegetables",
                    "Check blood sugar 2 hours after eating - target under 140 mg/dL",
                    "Best consumed as part of lunch rather than dinner for better glucose control"
                  ],
                  "portionAdvice": "A moderate portion is recommended. Be mindful of the fried components.",
                  "individualItems": [
                    {
                      "name": "Radish Cake (Daikon/White Radish & Rice Flour Cake)",
                      "contribution": "The primary bulk and source of Carbohydrates. Made from grated white radish (daikon) and rice flour, steamed into cakes, then cut and fried.",
                      "concernsForDiabetes": "While daikon itself is low in carbs, the rice flour significantly contributes to the Glycemic Index and carb count.",
                      "suitability": "caution"
                    },
                    {
                      "name": "Eggs",
                      "contribution": "Good source of Protein and healthy fats.",
                      "concernsForDiabetes": "Beneficial for stabilizing blood sugar.",
                      "suitability": "good"
                    },
                    {
                      "name": "Green Onions",
                      "contribution": "Adds flavor and minimal nutrients.",
                      "concernsForDiabetes": "Generally no concerns for diabetes.",
                      "suitability": "good"
                    },
                    {
                      "name": "Soy Sauce (condiment)",
                      "contribution": "Adds savory flavor.",
                      "concernsForDiabetes": "Can be high in sodium; choose low-sodium options if possible.",
                      "suitability": "moderate"
                    }
                  ]
                }
                If the image does not contain food (only utensils, empty plates, etc.), return:
                {
                  "isFood": false,
                  "foodName": null,
                  "confidence": 0,
                  "suitability": null,
                  "glycemicIndex": {"min": 0, "max": 0},
                  "carbs": {"min": 0, "max": 0},
                  "sugar": {"min": 0, "max": 0},
                  "fiber": {"min": 0, "max": 0},
                  "protein": {"min": 0, "max": 0},
                  "recommendations": [],
                  "portionAdvice": null,
                  "individualItems": []
                }
                `
              },
              {
                type: 'image_url',
                image_url: {
                  url: imageUrl,
                },
              },
            ],
          },
          ],
        }),
      });
    } catch (fetchError) {
      console.error('OpenRouter API fetch error:', fetchError);
      return NextResponse.json({
        error: 'Failed to connect to analysis service',
        details: fetchError instanceof Error ? fetchError.message : 'Network error'
      }, { status: 503 });
    }

    if (!response.ok) {
      let errorData;
      try {
        const errorText = await response.text();
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: errorText || 'Unknown error from analysis service' };
        }
      } catch {
        errorData = { error: 'Failed to read error response' };
      }

      console.error('OpenRouter API error:', { status: response.status, statusText: response.statusText, errorData });
      return NextResponse.json({
        error: 'Analysis service error',
        details: errorData,
        status: response.status
      }, { status: response.status });
    }

    const data = await response.json();
    const llmContent = data.choices[0]?.message?.content;

    if (!llmContent) {
      return NextResponse.json({ error: 'No content received from LLM' }, { status: 500 });
    }

    // Attempt to parse the JSON string from the LLM's content
    let parsedResult;
    try {
      // The LLM might return markdown, so extract the JSON part
      const jsonMatch = llmContent.match(/```json\n([\s\S]*?)\n```/);
      if (jsonMatch && jsonMatch[1]) {
        parsedResult = JSON.parse(jsonMatch[1]);
      } else {
        // If no markdown, try to parse directly (might be just JSON string)
        parsedResult = JSON.parse(llmContent);
      }
    } catch (parseError) {
      return NextResponse.json({ error: 'Failed to parse LLM response', details: llmContent }, { status: 500 });
    }

    return NextResponse.json(parsedResult);

  } catch (error) {
    console.error('Analysis API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
