import { NextRequest, NextResponse } from 'next/server';
import { auth, getAuth, currentUser } from '@clerk/nextjs/server';
import { createClient } from '@supabase/supabase-js';
import { addDays } from 'date-fns';

export async function POST(request: NextRequest) {
  try {
    const { imageUrl, targetLanguage = 'en' } = await request.json();
    const { userId } = await auth();
    const { getToken } = getAuth(request);
    const token = await getToken({ template: 'supabase' });

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated with Supabase' }, { status: 401 });
    }

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }

    // Check image size (base64 images can be very large on mobile)
    const imageSizeInBytes = imageUrl.length * 0.75; // Approximate size after base64 decoding
    const maxSizeInMB = 10;
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;

    if (imageSizeInBytes > maxSizeInBytes) {
      return NextResponse.json({
        error: `Image too large. Maximum size is ${maxSizeInMB}MB. Your image is approximately ${(imageSizeInBytes / (1024 * 1024)).toFixed(1)}MB.`
      }, { status: 413 });
    }

    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ error: 'OPENROUTER_API_KEY is not set' }, { status: 500 });
    }

    const languageInstruction = targetLanguage === 'en' ? '' : ` Provide all text content in ${targetLanguage === 'zh' ? 'Chinese' : targetLanguage}.`;

    let response;
    try {
      response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://diabetesplatescan.com',
          'X-Title': 'DiabetesPlateScan',
        },
        body: JSON.stringify({
          model: 'google/gemini-2.5-flash-preview-05-20',
          stream: true,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: `Analyze the food in this image for a diabetic patient.${languageInstruction}

IMPORTANT: Focus ONLY on the food items in the image. If you see utensils (spoons, forks, knives, plates, bowls, etc.) alongside food, ignore the utensils and analyze only the food. Do not return an error for images containing both food and utensils.

Provide the following information in a JSON format:
{
  "isFood": "boolean",
  "foodName": "string",
  "confidence": "number (percentage, e.g., 95)",
  "suitability": "good" | "moderate" | "caution",
  "glycemicIndex": {"min": "number", "max": "number"},
  "carbs": {"min": "number (grams)", "max": "number (grams)"},
  "sugar": {"min": "number (grams)", "max": "number (grams)"},
  "fiber": {"min": "number (grams)", "max": "number (grams)"},
  "protein": {"min": "number (grams)", "max": "number (grams)"},
  "recommendations": ["string"],
  "portionAdvice": "string",
  "individualItems": [
    {
      "name": "string",
      "contribution": "string",
      "concernsForDiabetes": "string",
      "suitability": "good" | "moderate" | "caution"
    }
  ]
}

If the image contains ONLY utensils or kitchen items with NO food visible, then set "isFood" to false.
If the image contains food (even with utensils present), set "isFood" to true and analyze the food.

CRITICAL INSTRUCTIONS:
1. **Nutritional Ranges**: Provide realistic ranges for glycemicIndex, carbs, protein, fiber, and sugar based on typical serving sizes and food variations.
2. **Suitability Assessment**:
   - "good": Low GI (≤55), low carbs (≤15g per serving), high fiber
   - "moderate": Medium GI (56-69), moderate carbs (16-30g per serving)
   - "caution": High GI (≥70), high carbs (>30g per serving), low fiber
3. **Recommendations**: Provide 3-4 specific, actionable recommendations for diabetic management
4. **Portion Advice**: Give specific serving size guidance with visual references

For portionAdvice, provide specific serving size guidance with visual references. For individualItems, provide detailed breakdown for each food component identified in the image.

If the image does not contain food (only utensils, empty plates, etc.), return:
{
  "isFood": false,
  "foodName": null,
  "confidence": 0,
  "suitability": null,
  "glycemicIndex": {"min": 0, "max": 0},
  "carbs": {"min": 0, "max": 0},
  "sugar": {"min": 0, "max": 0},
  "fiber": {"min": 0, "max": 0},
  "protein": {"min": 0, "max": 0},
  "recommendations": [],
  "portionAdvice": null,
  "individualItems": []
}`
              },
              {
                type: 'image_url',
                image_url: {
                  url: imageUrl,
                },
              },
            ],
          },
          ],
        }),
      });
    } catch (fetchError) {
      console.error('OpenRouter API fetch error:', fetchError);
      return NextResponse.json({
        error: 'Failed to connect to analysis service',
        details: fetchError instanceof Error ? fetchError.message : 'Network error'
      }, { status: 503 });
    }

    if (!response.ok) {
      let errorData;
      try {
        const errorText = await response.text();
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: errorText || 'Unknown error from analysis service' };
        }
      } catch {
        errorData = { error: 'Failed to read error response' };
      }

      console.error('OpenRouter API error:', { status: response.status, statusText: response.statusText, errorData });
      return NextResponse.json({
        error: 'Analysis service error',
        details: errorData,
        status: response.status
      }, { status: response.status });
    }

    if (!response.body) {
      return NextResponse.json({ error: 'No response body received' }, { status: 500 });
    }
    // Create a readable stream to process the streaming response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = response.body!.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let completeContent = '';

        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    completeContent += content;
                    // Send partial update
                    controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
                      type: 'partial',
                      content: completeContent
                    })}\n\n`));
                  }
                } catch (e) {
                  // Skip invalid JSON chunks
                }
              }
            }
          }

          // Process final content
          let parsedResult;
          try {
            // The LLM might return markdown, so extract the JSON part
            const jsonMatch = completeContent.match(/```json\n([\s\S]*?)\n```/);
            if (jsonMatch && jsonMatch[1]) {
              parsedResult = JSON.parse(jsonMatch[1]);
            } else {
              // If no markdown, try to parse directly (might be just JSON string)
              parsedResult = JSON.parse(completeContent);
            }

            // Send final result
            controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
              type: 'complete',
              result: parsedResult
            })}\n\n`));

          } catch (parseError) {
            controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
              type: 'error',
              error: 'Failed to parse LLM response',
              details: completeContent
            })}\n\n`));
          }

          controller.close();
        } catch (error) {
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
            type: 'error',
            error: 'Stream processing failed',
            details: error instanceof Error ? error.message : 'Unknown error'
          })}\n\n`));
          controller.close();
        }
      }
    });
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  } catch (error) {
    console.error('Streaming API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
