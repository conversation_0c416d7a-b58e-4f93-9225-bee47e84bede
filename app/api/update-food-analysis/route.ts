import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js'; // Import createClient
import { auth } from '@clerk/nextjs/server'; // Import auth helper

export async function PUT(request: NextRequest) {
  const { id, analysis_date: receivedAnalysisDate } = await request.json();

  // Convert the received date string to a Date object and then back to an ISO string
  // This ensures consistent formatting for Supabase
  const parsedAnalysisDate = new Date(receivedAnalysisDate);
  const formattedAnalysisDate = parsedAnalysisDate.toISOString();

  // Get the current user's ID and getToken helper from auth()
  const { userId: currentUserId, getToken } = await auth();

  if (!currentUserId) {
    console.error('API Route: User not authenticated via auth() helper');
    return NextResponse.json({
      error: 'Authentication required',
      details: 'User not authenticated',
      code: 'AUTH_REQUIRED'
    }, { status: 401 });
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('API Route: Missing Supabase URL or Anon Key environment variables.');
    return NextResponse.json({
      error: 'Server configuration error',
      details: 'Missing Supabase environment variables.',
      code: 'SERVER_ERROR'
    }, { status: 500 });
  }

  // Get the Clerk token for Supabase
  const clerkToken = await getToken({ template: 'supabase' });

  if (!clerkToken) {
    console.error('API Route: Clerk token is null or undefined.');
    return NextResponse.json({
      error: 'Authentication required',
      details: 'Clerk token not available',
      code: 'AUTH_TOKEN_MISSING'
    }, { status: 401 });
  }

  // Initialize Supabase client with the Clerk token
  const supabase = createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: { Authorization: `Bearer ${clerkToken}` },
    },
  });

  try {
    // Fetch the current record before attempting the update
    const { data: currentRecord, error: fetchError } = await supabase
      .from('food_analyses')
      .select('analysis_date')
      .match({ id: id, user_id: currentUserId })
      .single();

    if (fetchError) {
      console.error('API Route: Failed to fetch current record:', fetchError);
      return NextResponse.json({
        error: fetchError.message || 'Failed to fetch current record',
        details: fetchError.details || 'No details available',
        code: fetchError.code || 'UNKNOWN_SUPABASE_ERROR'
      }, { status: 500 });
    }

    const { data, error } = await supabase
      .from('food_analyses')
      .update({ analysis_date: formattedAnalysisDate })
      .match({ id: id, user_id: currentUserId }) // Use match instead of eq
      .select(); // Add select() to return the updated row

    if (error) {
      console.error('API Route: Supabase update operation failed:', error);
      // Ensure a consistent error response structure
      return NextResponse.json({
        error: error.message || 'Supabase update failed',
        details: error.details || 'No details available',
        hint: error.hint || 'No hint available',
        code: error.code || 'UNKNOWN_SUPABASE_ERROR'
      }, { status: 500 });
    }

    return NextResponse.json({ message: 'Analysis updated successfully', data }, { status: 200 });
  } catch (error: any) {
    console.error('API Route: Unexpected error in API route:', error);
    // Ensure a consistent error response structure for unexpected errors
    return NextResponse.json({
      error: 'Internal Server Error',
      details: error.message || 'An unexpected error occurred',
      code: 'INTERNAL_SERVER_ERROR'
    }, { status: 500 });
  }
}
