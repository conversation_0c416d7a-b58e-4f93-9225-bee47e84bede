import { NextRequest, NextResponse } from 'next/server';
import { getServerStripe } from '@/lib/stripe';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Use service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    const stripe = getServerStripe();

    // Get user profile from database
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('email', email)
      .single();

    if (profileError || !userProfile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (!userProfile.stripe_customer_id) {
      return NextResponse.json({ error: 'No Stripe customer ID found' }, { status: 404 });
    }

    if (!userProfile.stripe_subscription_id) {
      return NextResponse.json({ error: 'No Stripe subscription ID found' }, { status: 404 });
    }

    // Fetch subscription from Stripe
    const subscription = await stripe.subscriptions.retrieve(userProfile.stripe_subscription_id);

    console.log('Fetched subscription from Stripe:', {
      id: subscription.id,
      status: subscription.status,
      current_period_end: subscription.current_period_end,
      customer: subscription.customer,
    });

    // Update user profile with correct subscription data
    const updateData = {
      stripe_subscription_status: subscription.status,
      stripe_current_period_end: subscription.current_period_end && !isNaN(subscription.current_period_end)
        ? new Date(subscription.current_period_end * 1000).toISOString()
        : null,
      stripe_plan_id: subscription.items.data[0]?.price.id,
      updated_at: new Date().toISOString(),
    };

    // If subscription is active, update trial status and subscription type
    if (subscription.status === 'active') {
      updateData.subscription_type = 'paid';
      updateData.is_trial_active = false;
    }

    const { error: updateError, data: updatedData } = await supabase
      .from('user_profiles')
      .update(updateData)
      .eq('email', email)
      .select();

    if (updateError) {
      console.error('Error updating subscription data:', updateError);
      return NextResponse.json({ error: 'Failed to update subscription data' }, { status: 500 });
    }

    console.log('Successfully synced subscription data for:', email);

    return NextResponse.json({
      success: true,
      message: 'Subscription data synced successfully',
      data: {
        email,
        stripe_subscription_status: subscription.status,
        stripe_current_period_end: updateData.stripe_current_period_end,
        stripe_plan_id: updateData.stripe_plan_id,
        updated: updatedData?.[0],
      }
    });

  } catch (error) {
    console.error('Error syncing subscription:', error);
    return NextResponse.json(
      { 
        error: 'Failed to sync subscription data',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
