import { NextRequest, NextResponse } from 'next/server';
import { getServerStripe } from '@/lib/stripe';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Use service role key for webhook operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature')!;
  const stripe = getServerStripe();

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
  }

  try {
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionChange(event.data.object as Stripe.Subscription);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;
      
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 });
  }
}

async function handleSubscriptionChange(subscription: Stripe.Subscription) {
  const customerId = subscription.customer as string;

  console.log('Updating subscription for customer:', customerId, 'status:', subscription.status);

  // Find user by stripe_customer_id and update subscription info
  const updateData: any = {
    stripe_subscription_id: subscription.id,
    stripe_subscription_status: subscription.status,
    stripe_current_period_end: subscription.current_period_end && !isNaN(subscription.current_period_end)
      ? new Date(subscription.current_period_end * 1000).toISOString()
      : null,
    stripe_plan_id: subscription.items.data[0]?.price.id,
    updated_at: new Date().toISOString(),
  };

  // If subscription is active, update trial status and subscription type
  if (subscription.status === 'active') {
    updateData.subscription_type = 'paid';
    updateData.is_trial_active = false;
  } else if (subscription.status === 'trialing') {
    updateData.subscription_type = 'trial';
    updateData.is_trial_active = true;
  } else if (subscription.status === 'incomplete') {
    // Only update to incomplete if current status is not already active
    // This prevents overwriting active subscriptions with incomplete status
    const { data: currentProfile } = await supabase
      .from('user_profiles')
      .select('stripe_subscription_status')
      .eq('stripe_customer_id', customerId)
      .single();

    if (currentProfile?.stripe_subscription_status === 'active') {
      console.log('Skipping incomplete status update - subscription is already active');
      return; // Don't update if already active
    }
    updateData.subscription_type = 'pending';
  }

  const { error, data } = await supabase
    .from('user_profiles')
    .update(updateData)
    .eq('stripe_customer_id', customerId)
    .select();

  if (error) {
    console.error('Error updating subscription:', error);
    throw error; // Re-throw to trigger webhook retry
  } else {
    console.log('Successfully updated subscription for customer:', customerId, 'affected rows:', data?.length);
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const customerId = subscription.customer as string;

  console.log('Handling subscription deletion for customer:', customerId);

  const { error, data } = await supabase
    .from('user_profiles')
    .update({
      stripe_subscription_id: null,
      stripe_subscription_status: 'canceled',
      stripe_current_period_end: null,
      stripe_plan_id: null,
      subscription_type: 'trial', // Reset to trial
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_customer_id', customerId)
    .select();

  if (error) {
    console.error('Error handling subscription deletion:', error);
    throw error; // Re-throw to trigger webhook retry
  } else {
    console.log('Successfully handled subscription deletion for customer:', customerId, 'affected rows:', data?.length);
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  // Handle successful payment - could send confirmation email, etc.
  console.log('Payment succeeded for invoice:', invoice.id);
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  // Handle failed payment - could send notification, etc.
  console.log('Payment failed for invoice:', invoice.id);
}
