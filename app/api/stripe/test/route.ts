import { NextResponse } from 'next/server';
import { getServerStripe } from '@/lib/stripe';

export async function GET() {
  try {
    const stripe = getServerStripe();
    
    // Test that Stripe is properly configured
    const account = await stripe.accounts.retrieve();
    
    return NextResponse.json({ 
      success: true, 
      message: 'Stripe configuration is working',
      accountId: account.id 
    });
  } catch (error) {
    console.error('Stripe test error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Stripe configuration failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
