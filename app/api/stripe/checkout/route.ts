import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getServerStripe } from '@/lib/stripe';
import { createClient } from '@supabase/supabase-js';

// Helper function to get the correct origin URL
function getOriginUrl(request: NextRequest): string {
  // Try to get origin from headers first
  const origin = request.headers.get('origin');
  if (origin) return origin;

  // Try to construct from host header
  const host = request.headers.get('host');
  if (host) {
    const protocol = request.headers.get('x-forwarded-proto') ||
                    (host.includes('localhost') ? 'http' : 'https');
    return `${protocol}://${host}`;
  }

  // Fallback to environment variable
  if (process.env.NEXT_PUBLIC_SITE_URL) return process.env.NEXT_PUBLIC_SITE_URL;

  // Final fallback
  return process.env.NODE_ENV === 'production'
    ? 'https://diabetesplatescan.com'
    : 'http://localhost:3000';
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    const { searchParams } = new URL(request.url);
    const priceId = searchParams.get('priceId');
    
    console.log('Checkout request:', { userId, priceId });

    if (!userId) {
      console.log('No userId found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!priceId) {
      console.log('No priceId provided');
      return NextResponse.json({ error: 'Price ID is required' }, { status: 400 });
    }

    const stripe = getServerStripe();

    // Get user profile from Supabase
    const { getToken } = await auth();
    const token = await getToken({ template: 'supabase' });
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: { Authorization: `Bearer ${token}` },
      },
    });

    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('email, stripe_customer_id')
      .eq('user_id', userId)
      .single();

    console.log('User profile query result:', { userProfile, profileError });

    if (profileError || !userProfile?.email) {
      console.log('User profile not found or no email');
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    let customerId = userProfile.stripe_customer_id;

    // Create or retrieve Stripe customer
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: userProfile.email,
        metadata: {
          userId: userId,
        },
      });
      customerId = customer.id;

      // Update user profile with customer ID
      await supabase
        .from('user_profiles')
        .update({ stripe_customer_id: customerId })
        .eq('user_id', userId);
    }

    // Create checkout session
    const origin = getOriginUrl(request);
    console.log('Creating checkout session with:', {
      customer: customerId,
      priceId: priceId,
      origin: origin,
      headers: {
        origin: request.headers.get('origin'),
        host: request.headers.get('host'),
        'x-forwarded-proto': request.headers.get('x-forwarded-proto')
      }
    });

    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      allow_promotion_codes: true, // Enable discount/coupon code input
      success_url: `${origin}/dashboard/profile?tab=pricing&success=true`,
      cancel_url: `${origin}/dashboard/profile?tab=pricing&canceled=true`,
      metadata: {
        userId: userId,
        priceId: priceId,
      },
    });

    console.log('Checkout session created:', session.id);
    
    // Redirect to Stripe Checkout
    return NextResponse.redirect(session.url!);
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}
