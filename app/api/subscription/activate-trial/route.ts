import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile from Supabase
    const { getToken } = await auth();
    const token = await getToken({ template: 'supabase' });
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: { Authorization: `Bearer ${token}` },
      },
    });

    // Check if user has already used trial
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('trial_used, trial_start_date, trial_end_date')
      .eq('user_id', userId)
      .single();

    if (profileError) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (userProfile.trial_used) {
      return NextResponse.json({ error: 'Trial already used' }, { status: 400 });
    }

    // Activate trial - 7 days from now
    const trialStartDate = new Date();
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 7);

    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({
        trial_used: true,
        trial_start_date: trialStartDate.toISOString(),
        trial_end_date: trialEndDate.toISOString(),
      })
      .eq('user_id', userId);

    if (updateError) {
      console.error('Error activating trial:', updateError);
      return NextResponse.json({ error: 'Failed to activate trial' }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      trialEndDate: trialEndDate.toISOString() 
    });
  } catch (error) {
    console.error('Error activating trial:', error);
    return NextResponse.json(
      { error: 'Failed to activate trial' },
      { status: 500 }
    );
  }
}
