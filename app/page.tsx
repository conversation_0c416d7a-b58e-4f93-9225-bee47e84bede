"use client";

import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Camera, Users, Heart, LineChart, Lightbulb, Clock, FileText, Brain } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import Link from "next/link"
import Image from "next/image" // Import Image component
import { useTranslation, Trans } from 'react-i18next'; // Import useTranslation and Trans
import { useState, FormEvent } from 'react'; // Import useState and FormEvent for form handling
import PricingDeck from '@/components/pricing/PricingDeck';

export default function HomePage() {
  const { t } = useTranslation(); // Initialize useTranslation hook

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');
  const [isError, setIsError] = useState(false);

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setStatusMessage('');
    setIsError(false);

    // Client-side validation
    if (!name || !email || !message) {
      setStatusMessage('All fields are required.');
      setIsError(true);
      setLoading(false);
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setStatusMessage('Invalid email format.');
      setIsError(true);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, message }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatusMessage('Message sent successfully!');
        setIsError(false);
        setName('');
        setEmail('');
        setMessage('');
      } else {
        setStatusMessage(data.error || 'Failed to send message.');
        setIsError(true);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setStatusMessage('An unexpected error occurred.');
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 overflow-x-hidden">
      {/* Hero Section */}
      <section id="hero" className="relative overflow-hidden pt-16 pb-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="text-center md:text-left">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                <Trans i18nKey="homePage.hero.title" components={{ span: <span key="hero-title-span" className="text-blue-600" /> }} />
              </h1>
              <p className="text-xl text-gray-600 mb-8 max-w-md md:max-w-none text-center">
                {t('homePage.hero.description')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                <Link href="/dashboard/analyze">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg">
                    {t('homePage.hero.analyzeFoodButton')}
                  </Button>
                </Link>
              </div>
            </div>
            <div className="flex justify-center md:justify-end">
              {/* Placeholder Hero Image */}
              <Image
                src="/img_hero_woman.png"
                alt="A woman managing her diabetes with the DiabetesPlateScan app"
                width={900}
                height={700}
                className="rounded-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* About Us Section */}
      <section id="about" className="py-20 px-4 bg-white">
        <div className="container mx-auto max-w-6xl text-center">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('aboutUs.title')}</h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto mb-4">
            {t('aboutUs.paragraph1')}
          </p>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            {t('aboutUs.paragraph2')}
          </p>
        </div>
      </section>

      {/* Designed for your health Section */}
      <section id="features" className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('homePage.features.title')}</h2>
            <p className="text-xl text-gray-600">
              {t('homePage.features.description')}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div className="flex justify-center md:justify-end">
              <Image
                src="/img_Mobile_Mock.png"
                alt="Mobile app mock-up"
                width={400}
                height={600}
                className="rounded-xl"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Camera className="h-6 w-6 text-blue-600" />
                    <CardTitle className="text-xl">{t('homePage.features.card1Title')}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {t('homePage.features.card1Description')}
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <LineChart className="h-6 w-6 text-blue-600" />
                    <CardTitle className="text-xl">{t('homePage.features.card2Title')}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {t('homePage.features.card2Description')}
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Lightbulb className="h-6 w-6 text-blue-600" />
                    <CardTitle className="text-xl">{t('homePage.features.card3Title')}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {t('homePage.features.card3Description')}
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="h-6 w-6 text-blue-600" />
                    <CardTitle className="text-xl">{t('homePage.features.card4Title')}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {t('homePage.features.card4Description')}
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <FileText className="h-6 w-6 text-blue-600" />
                    <CardTitle className="text-xl">{t('homePage.features.card5Title')}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {t('homePage.features.card5Description')}
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Brain className="h-6 w-6 text-blue-600" />
                    <CardTitle className="text-xl">{t('homePage.features.card6Title')}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {t('homePage.features.card6Description')}
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Simple. Fast. Accurate. Section */}
      <section id="how-it-works" className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('homePage.howItWorks.title')}</h2>
            <p className="text-xl text-gray-600">{t('homePage.howItWorks.description')}</p>
          </div>
          <div className="grid md:grid-cols-3 gap-12 text-center">
            <div>
              <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-3xl font-bold mx-auto mb-6">
                1
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">{t('homePage.howItWorks.step1Title')}</h3>
              <p className="text-lg text-gray-600">
                {t('homePage.howItWorks.step1Description')}
              </p>
            </div>
            <div>
              <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center text-white text-3xl font-bold mx-auto mb-6">
                2
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">{t('homePage.howItWorks.step2Title')}</h3>
              <p className="text-lg text-gray-600">
                {t('homePage.howItWorks.step2Description')}
              </p>
            </div>
            <div>
              <div className="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center text-white text-3xl font-bold mx-auto mb-6">
                3
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">{t('homePage.howItWorks.step3Title')}</h3>
              <p className="text-lg text-gray-600">
                {t('homePage.howItWorks.step3Description')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted by thousands Section */}
      <section id="testimonials" className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('homePage.testimonials.title')}</h2>
            <p className="text-xl text-gray-600">{t('homePage.testimonials.description')}</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg p-6">
              <CardContent className="text-center">
                <p className="text-lg italic text-gray-700 mb-6">
                  {t('homePage.testimonials.testimonial1')}
                </p>
                <div className="flex flex-col items-center">
                    <Image
                      src="/testimonials/img_SarahJohnson.png"
                      alt="Sarah Johnson - a happy user of DiabetesPlateScan"
                      width={150}
                      height={150}
                      className="rounded-full mb-2"
                    />
                  <p className="font-semibold text-gray-900">{t('homePage.testimonials.author1')}</p>
                  <p className="text-sm text-gray-600">{t('homePage.testimonials.role1')}</p>
                </div>
              </CardContent>
            </Card>
            <Card className="border-0 shadow-lg p-6">
              <CardContent className="text-center">
                <p className="text-lg italic text-gray-700 mb-6">
                  {t('homePage.testimonials.testimonial2')}
                </p>
                <div className="flex flex-col items-center">
                    <Image
                      src="/testimonials/img_MichaelChen.png"
                      alt="Michael Chen - a satisfied user of DiabetesPlateScan"
                      width={150}
                      height={150}
                      className="rounded-full mb-2"
                    />
                  <p className="font-semibold text-gray-900">{t('homePage.testimonials.author2')}</p>
                  <p className="text-sm text-gray-600">{t('homePage.testimonials.role2')}</p>
                </div>
              </CardContent>
            </Card>
            <Card className="border-0 shadow-lg p-6">
              <CardContent className="text-center">
                <p className="text-lg italic text-gray-700 mb-6">
                  {t('homePage.testimonials.testimonial3')}
                </p>
                <div className="flex flex-col items-center">
                    <Image
                      src="/testimonials/img_DrAnya.png"
                      alt="Emily Rodriguez - a user of DiabetesPlateScan who recommends the app"
                      width={150}
                      height={150}
                      className="rounded-full mb-2"
                    />
                  <p className="font-semibold text-gray-900">{t('homePage.testimonials.author3')}</p>
                  <p className="text-sm text-gray-600">{t('homePage.testimonials.role3')}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Choose your plan Section */}
      <section id="pricing" className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('homePage.pricing.title')}</h2>
            <p className="text-xl text-gray-600">{t('homePage.pricing.description')}</p>
          </div>
          <PricingDeck
            showForLoggedInUser={false}
            onPlanSelect={() => {
              // For homepage, redirect to pricing page for more details
              window.location.href = '/pricing';
            }}
          />
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('homePage.contact.title')}</h2>
            <p className="text-xl text-gray-600">{t('homePage.contact.description')}</p>
          </div>
          <div className="max-w-md mx-auto space-y-4">
            <form onSubmit={handleSubmit}>
              <div>
                <Label htmlFor="contact-name">{t('homePage.contact.nameLabel')}</Label>
                <Input
                  id="contact-name"
                  placeholder={t('homePage.contact.namePlaceholder')}
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  disabled={loading}
                  className="text-base"
                />
              </div>
              <div>
                <Label htmlFor="contact-email">{t('homePage.contact.emailLabel')}</Label>
                <Input
                  id="contact-email"
                  type="email"
                  placeholder={t('homePage.contact.emailPlaceholder')}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading}
                  className="text-base"
                />
              </div>
              <div>
                <Label htmlFor="contact-message">{t('homePage.contact.messageLabel')}</Label>
                <Textarea
                  id="contact-message"
                  placeholder={t('homePage.contact.messagePlaceholder')}
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={5}
                  disabled={loading}
                  className="text-base"
                />
              </div>
              <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 mt-4" disabled={loading}>
                {loading ? 'Sending...' : t('homePage.contact.sendMessageButton')}
              </Button>
              {statusMessage && (
                <p className={`mt-4 text-center ${isError ? 'text-red-500' : 'text-green-500'}`}>
                  {statusMessage}
                </p>
              )}
            </form>
          </div>
        </div>
      </section>

      {/* Frequently asked questions Section */}
      <section id="faq" className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('homePage.faq.title')}</h2>
            <p className="text-xl text-gray-600">{t('homePage.faq.description')}</p>
          </div>
          <Accordion type="single" collapsible className="w-full max-w-3xl mx-auto">
            <AccordionItem value="item-1">
              <AccordionTrigger><div className="w-full text-left">{t('homePage.faq.question1')}</div></AccordionTrigger>
              <AccordionContent>
                {t('homePage.faq.answer1')}
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-2">
              <AccordionTrigger><div className="w-full text-left">{t('homePage.faq.question2')}</div></AccordionTrigger>
              <AccordionContent>
                {t('homePage.faq.answer2')}
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-3">
              <AccordionTrigger><div className="w-full text-left">{t('homePage.faq.question3')}</div></AccordionTrigger>
              <AccordionContent>
                {t('homePage.faq.answer3')}
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-4">
              <AccordionTrigger><div className="w-full text-left">{t('homePage.faq.question5')}</div></AccordionTrigger>
              <AccordionContent>
                {t('homePage.faq.answer5')}
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-5">
              <AccordionTrigger><div className="w-full text-left">{t('homePage.faq.question6')}</div></AccordionTrigger>
              <AccordionContent>
                {t('homePage.faq.answer6')}
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-6">
              <AccordionTrigger><div className="w-full text-left">{t('homePage.faq.question7')}</div></AccordionTrigger>
              <AccordionContent>
                {t('homePage.faq.answer7')}
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-7">
              <AccordionTrigger><div className="w-full text-left">{t('homePage.faq.question8')}</div></AccordionTrigger>
              <AccordionContent>
                {t('homePage.faq.answer8')}
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-8">
              <AccordionTrigger><div className="w-full text-left">{t('homePage.faq.question9')}</div></AccordionTrigger>
              <AccordionContent>
                {t('homePage.faq.answer9')}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </section>

      {/* Featured On Section */}
      <section id="featured-on" className="py-20 px-4 bg-white">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">{t('homePage.featuredOn.title')}</h2>
            <p className="text-xl text-gray-600">{t('homePage.featuredOn.description')}</p>
          </div>
          <div className="flex justify-center">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow p-8 bg-gradient-to-br from-gray-50 to-white">
              <CardContent className="flex items-center justify-center">
                <a href="https://dang.ai/" target="_blank" rel="noopener noreferrer" className="block">
                  <img
                    src="https://cdn.prod.website-files.com/63d8afd87da01fb58ea3fbcb/6487e2868c6c8f93b4828827_dang-badge.png"
                    alt="Dang.ai - Featured Platform"
                    style={{ width: '150px', height: '54px' }}
                    width="150"
                    height="54"
                    className="hover:scale-105 transition-transform duration-200"
                  />
                </a>
              </CardContent>
            </Card>
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow p-8 bg-gradient-to-br from-gray-50 to-white">
              <CardContent className="flex items-center justify-center">
                <a href="https://www.toolpilot.ai/" target="_blank" rel="noopener noreferrer" className="block">
                  <Image
                    src="/aff_SPToolPilot.png"
                    alt="SPToolPilot - Featured Platform"
                    style={{ width: '150px', height: '54px' }}
                    width="150"
                    height="54"
                    className="hover:scale-105 transition-transform duration-200"
                  />
                </a>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Take control of your diabetes today Section */}
      <section id="cta" className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl p-12 text-center text-white">
            <h2 className="text-4xl font-bold mb-4">{t('homePage.cta.title')}</h2>
            <p className="text-xl mb-8 opacity-90">
              {t('homePage.cta.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard/analyze">
                <Button
                  size="lg"
                  variant="secondary"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg"
                >
                  {t('homePage.cta.analyzeFoodButton')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
