"use client";

import type React from "react"
import { Inter } from "next/font/google"
import Script from "next/script"
import { metadata } from "./metadata"
import "./globals.css"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer" // Import the new Footer component
import { Clerk<PERSON>rovider } from "@clerk/nextjs"
import Head from "next/head";
import { I18nProvider } from "@/components/i18n-provider" // Import I18nProvider
import UserProfileInitializer from "@/components/UserProfileInitializer";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({ subsets: ["latin"] })


export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // TODO: Replace with real language detection (cookie, header, etc.)
  const language = "en";

  const posthogScript = `
  !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src="https://us.i.posthog.com".replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init Ie Ts Ms Ee Es Rs capture Ge calculateEventProperties Os register register_once register_for_session unregister unregister_for_session js getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Ds Fs createPersonProfile Ls Ps opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Cs debug I As getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
  posthog.init('${process.env.NEXT_PUBLIC_POSTHOG_API_KEY}', {
      api_host: 'https://us.i.posthog.com',
      defaults: '2025-05-24',
      person_profiles: 'identified_only',
  })
`;

  return (
    <ClerkProvider>
      <html lang={language} suppressHydrationWarning>
        <Head>
          <link rel="icon" href="/favicon.ico" sizes="any" />
        </Head>
        <body className={`${inter.className} flex flex-col min-h-screen`}>
          <UserProfileInitializer /> {/* Add the initializer component here */}
          <I18nProvider language={language}>
            <Navigation />
            <main className="flex-grow">{children}</main>
            <Footer />
            <Toaster />
          </I18nProvider>
          {/* Tracking Scripts */}
          {/* umami */}
          {process.env.NEXT_PUBLIC_UMAMI_SCRIPT_URL && process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID && (
            <Script
              defer
              src={process.env.NEXT_PUBLIC_UMAMI_SCRIPT_URL}
              data-website-id={process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID}
            />
          )}
          {/* posthog */}
          {process.env.NEXT_PUBLIC_POSTHOG_API_KEY && (
            <Script id="posthog-script" strategy="afterInteractive">
              {posthogScript}
            </Script>
          )}
        </body>
      </html>
    </ClerkProvider>
  )
}
