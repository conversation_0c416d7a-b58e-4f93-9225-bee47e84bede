"use client"

import React, { useState, useEffect, useRef } from "react"
import { useRouter, usePathname } from 'next/navigation';
import { Camera, Upload, X, Loader2, AlertCircle, CheckCircle, AlertTriangle, Crown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useUser } from '@clerk/nextjs';
import { useClerkSupabase } from '@/lib/supabaseClient';
import { useTranslation } from 'react-i18next';
import i18n from '@/lib/i18n';
import { differenceInDays } from 'date-fns';
import { useSubscription } from '@/hooks/useSubscription';

interface NutritionalRange {
  min: number
  max: number
}

interface AnalysisResult {
  isFood: boolean
  foodName: string | null
  confidence: number
  suitability: "good" | "moderate" | "caution" | null
  glycemicIndex: NutritionalRange
  carbs: NutritionalRange
  sugar: NutritionalRange
  fiber: NutritionalRange
  protein: NutritionalRange
  recommendations: string[]
  portionAdvice: string | null
  individualItems: IndividualFoodItem[]
}

interface IndividualFoodItem {
  name: string
  contribution: string
  concernsForDiabetes: string
  suitability: "good" | "moderate" | "caution"
}

export default function AnalyzePage() {
  const { t } = useTranslation();
  const { user, isLoaded } = useUser();
  const supabase = useClerkSupabase();
  const router = useRouter();
  const pathname = usePathname();
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null)
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [streamingContent, setStreamingContent] = useState<string>('');
  const [notFoodError, setNotFoodError] = useState<string | null>(null); // New state for "not food" error
  const [showAnalysisResultsCard, setShowAnalysisResultsCard] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null)
  const cameraInputRef = useRef<HTMLInputElement>(null)
  const [userLanguage, setUserLanguage] = useState('en');
  const { subscription, loading: subscriptionLoading } = useSubscription();

  useEffect(() => {
    const fetchUserData = async () => {
      if (isLoaded && user && supabase) {
        // Always fetch fresh data by appending a timestamp param to bypass any cache
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .select('language, trial_start_date, trial_end_date, is_trial_active, has_trial_ever_been_active')
          .eq('user_id', user.id)
          .single();

        if (profileError) {
          if (profileError.code === 'PGRST116') {
            // No user_profiles record, redirect to onboarding
            router.replace('/dashboard/profile');
            return;
          } else {
            console.error('Error fetching user profile data:', profileError);
          }
        } else if (profileData) {
          setUserLanguage(profileData.language || 'en');
          i18n.changeLanguage(profileData.language || 'en');
        }
      }
    };

    if (isLoaded) {
      fetchUserData();
    }
  }, [user, supabase, isLoaded, router, pathname]);

  // Helper function to compress image
  const compressImage = (file: File, maxWidth: number = 1024, quality: number = 0.8): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxWidth) {
            width = (width * maxWidth) / height;
            height = maxWidth;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
        resolve(compressedDataUrl);
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    setUploadError(null); // Clear previous errors

    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setUploadError(t('analyzePage.unsupportedFileType'));
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        setUploadError(t('analyzePage.fileTooLarge', { maxSize: '10MB' }));
        return;
      }

      try {
        // Compress image if it's large (especially for mobile photos)
        let imageDataUrl: string;
        if (file.size > 1024 * 1024) { // If larger than 1MB, compress
          imageDataUrl = await compressImage(file, 1024, 0.8);
        } else {
          // For smaller files, use as-is
          imageDataUrl = await new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target?.result as string);
            reader.readAsDataURL(file);
          });
        }

        setSelectedImage(imageDataUrl);
        setAnalysisResult(null);
        setShowAnalysisResultsCard(false); // Hide results card when new image is selected
        setNotFoodError(null); // Clear "not food" error when new image is selected
      } catch (error) {
        console.error('Error processing image:', error);
        setUploadError('Failed to process image. Please try again.');
      }
    }
  };

  const handleAnalyzeStreaming = async () => {
    if (!selectedImage) return;

    setIsAnalyzing(true);
    setAnalysisResult(null);
    setStreamingContent('');
    setIsSaving(false);
    setNotFoodError(null);

    // Check scan limits before proceeding
    if (!subscription.hasUnlimitedScans) {
      setIsAnalyzing(false);
      return;
    }

    try {
      const response = await fetch('/api/analyze-food-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl: selectedImage, targetLanguage: userLanguage }),
      });

      if (!response.ok) {
        let errorData: any = { error: 'Unknown error' };
        try {
          const errorText = await response.text();
          if (errorText) {
            try {
              errorData = JSON.parse(errorText);
            } catch {
              errorData = { error: errorText };
            }
          }
        } catch (fetchError) {
          console.error('Failed to read error response:', fetchError);
          errorData = { error: 'Failed to read error response' };
        }

        console.error('API call failed:', { status: response.status, statusText: response.statusText, errorData });

        // Show more specific error messages
        let errorMessage = errorData.error || 'Unknown error';
        if (response.status === 413) {
          errorMessage = 'Image too large. Please try a smaller image or take a new photo.';
        } else if (response.status === 503) {
          errorMessage = 'Analysis service temporarily unavailable. Please try again.';
        } else if (response.status >= 500) {
          errorMessage = 'Server error. Please try again later.';
        }

        alert(t('analyzePage.analysisFailed', { error: errorMessage }));
        return;
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.type === 'partial') {
                setStreamingContent(data.content);
              } else if (data.type === 'complete') {
                const result: AnalysisResult = data.result;

                if (!result.isFood) {
                  setAnalysisResult(null);
                  setShowAnalysisResultsCard(false);
                  setNotFoodError(t('analyzePage.notFoodMessage'));
                  return;
                }

                setAnalysisResult(result);
                setShowAnalysisResultsCard(true);
                setStreamingContent('');
              } else if (data.type === 'error') {
                console.error('Streaming error:', data);
                alert(t('analyzePage.analysisFailed', { error: data.error || 'Unknown error' }));
                return;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }

    } catch (error) {
      console.error('Error during streaming analysis:', error);
      alert(t('analyzePage.unexpectedError'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleAnalyze = async () => {
    // Always use streaming for better UX
    return handleAnalyzeStreaming();
  }

  const executeSaveAnalysis = async () => {
    if (!analysisResult || !user || !supabase || !analysisResult.isFood) return; // Ensure it's food before saving

    setIsSaving(true);
    setShowConfirmDialog(false);

    try {
      const clerkUserId = user.id;

      // Calculate averages for backward compatibility
      const avgGI = Math.round((analysisResult.glycemicIndex.min + analysisResult.glycemicIndex.max) / 2);
      const avgCarbs = Math.round((analysisResult.carbs.min + analysisResult.carbs.max) / 2);
      const avgProtein = Math.round((analysisResult.protein.min + analysisResult.protein.max) / 2);
      const avgFiber = Math.round((analysisResult.fiber.min + analysisResult.fiber.max) / 2);

      const { data, error: supabaseError } = await supabase
        .from('food_analyses')
        .insert([
          {
            user_id: clerkUserId,
            analysis_date: new Date().toISOString(),
            food_name: analysisResult.foodName,
            suitability: analysisResult.suitability,
            gi: avgGI,
            carbs_g: avgCarbs,
            protein_g: avgProtein,
            fiber_g: avgFiber,
            components: analysisResult.individualItems,
            nutritional_ranges: {
              glycemicIndex: analysisResult.glycemicIndex,
              carbs: analysisResult.carbs,
              protein: analysisResult.protein,
              fiber: analysisResult.fiber,
              sugar: analysisResult.sugar
            }
          }
        ]);

      if (supabaseError) {
        console.error('Error saving analysis to Supabase:', supabaseError);
        if (supabaseError.message) console.error('Supabase Error Message:', supabaseError.message);
        if (supabaseError.details) console.error('Supabase Error Details:', supabaseError.details);
        if (supabaseError.hint) console.error('Supabase Error Hint:', supabaseError.hint);
        if (supabaseError.code) console.error('Supabase Error Code:', supabaseError.code);
      } else {
        setShowSuccessMessage(true);
        setTimeout(() => {
          setShowSuccessMessage(false);
        }, 3000);
      }
    } catch (error) {
      console.error('Error during save:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleConfirmSaveClick = () => {
    if (analysisResult && analysisResult.isFood) { // Only show confirm dialog if it's food
      setShowConfirmDialog(true);
    }
  };

  const getSuitabilityColor = (suitability: string | null) => {
    if (!suitability) return "text-gray-600 bg-gray-50 border-gray-200";
    switch (suitability) {
      case "good":
        return "text-green-600 bg-green-50 border-green-200"
      case "moderate":
        return "text-yellow-600 bg-yellow-50 border-yellow-200"
      case "caution":
        return "text-red-600 bg-red-50 border-red-200"
      default:
        return "text-gray-600 bg-gray-50 border-gray-200"
    }
  }

  const getSuitabilityIcon = (suitability: string | null) => {
    if (!suitability) return null;
    switch (suitability) {
      case "good":
        return <CheckCircle className="h-5 w-5" />
      case "moderate":
        return <AlertTriangle className="h-5 w-5" />
      case "caution":
        return <AlertCircle className="h-5 w-5" />
      default:
        return null
    }
  }

  const getSuitabilityBorderClass = (suitability: string | null) => {
    if (!suitability) return "border-gray-300";
    switch (suitability) {
      case "good":
        return "border-green-500";
      case "moderate":
        return "border-yellow-500";
      case "caution":
        return "border-red-500";
      default:
        return "border-gray-300";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto w-[95%] sm:w-[90%] md:w-[70%] max-w-3xl px-2 sm:px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('analyzePage.title')}</h1>
          <p className="text-gray-600">
            {t('analyzePage.description')}
          </p>
        </div>

        <div className="flex flex-col items-center gap-8">
          {/* Image Upload Section */}
          <Card className="w-full max-w-lg mx-2 sm:mx-0">
            <CardHeader>
              <CardTitle>{t('analyzePage.uploadCardTitle')}</CardTitle>
              <CardDescription>{t('analyzePage.uploadCardDescription')}</CardDescription>
            </CardHeader>
            <CardContent className="px-3 sm:px-6">
              {!selectedImage ? (
                <div className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
                    <div className="flex flex-col items-center space-y-4">
                      <Button onClick={() => fileInputRef.current?.click()} className="bg-blue-600 hover:bg-blue-700">
                        <Upload className="mr-2 h-4 w-4" />
                        {t('analyzePage.uploadImageButton')}
                      </Button>
                      <p className="text-sm text-gray-500">{t('analyzePage.supportedFormats')}</p>
                    </div>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                  <input
                    ref={cameraInputRef} // Use the new ref
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    capture="environment" // Keep capture for camera
                  />
                  <Button
                    variant="outline"
                    onClick={() => cameraInputRef.current?.click()} // Trigger camera input
                    className="w-16 h-16 rounded-full p-0 flex items-center justify-center mx-auto bg-yellow-400 hover:bg-yellow-500"
                  >
                    <Camera className="h-8 w-8" />
                  </Button>
                  {uploadError && (
                    <Alert variant="destructive">
                      <AlertDescription>{uploadError}</AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="relative">
                    <img
                      src={selectedImage || "/placeholder.svg"}
                      alt={t('analyzePage.selectedMealAlt')}
                      className="w-full aspect-square object-cover rounded-lg"
                    />
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => {
                        setSelectedImage(null)
                        setAnalysisResult(null)
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  {/* Conditional rendering of Analyze button based on subscription/trial status */}
                  {(() => {
                    if (subscriptionLoading) {
                      return (
                        <Button disabled className="w-full h-14 text-lg">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </Button>
                      );
                    }

                    if (subscription.hasUnlimitedScans) {
                      // User has unlimited scans (trial or paid)
                      return (
                        <>
                          {subscription.isTrialActive && subscription.trialEndDate && (
                            <div className="text-center text-sm text-gray-600 mb-2">
                              {(() => {
                                const daysLeft = differenceInDays(subscription.trialEndDate, new Date());
                                return daysLeft > 0
                                  ? t('analyzePage.trialDaysLeft', { count: daysLeft })
                                  : t('analyzePage.trialEndingToday');
                              })()}
                            </div>
                          )}
                          {subscription.isTrialActive && (
                            <Button
                              onClick={() => router.push('/pricing')}
                              className="w-full bg-blue-600 hover:bg-blue-700 h-14 text-sm sm:text-lg mb-2"
                            >
                              <Crown className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                              <span className="hidden sm:inline">{t('analyzePage.upgradeForUnlimitedScans')}</span>
                              <span className="sm:hidden">{t('analyzePage.upgradeForUnlimitedScansShort')}</span>
                            </Button>
                          )}
                          <Button
                            onClick={handleAnalyze}
                            disabled={isAnalyzing}
                            className="w-full bg-green-600 hover:bg-green-700 h-14 text-lg"
                          >
                            {isAnalyzing ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                {t('analyzePage.analyzingButton')}
                              </>
                            ) : (
                              t('analyzePage.analyzeFoodButton')
                            )}
                          </Button>
                        </>
                      );
                    } else {
                      // No access - trial expired or no subscription
                      return (
                        <>
                          <div className="text-center text-sm text-gray-600 mb-2">
                            {t('analyzePage.trialExpired')}
                          </div>
                          <Button
                            onClick={() => router.push('/pricing')}
                            className="w-full bg-blue-600 hover:bg-blue-700 h-14 text-sm sm:text-lg mb-2"
                          >
                            <Crown className="mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                            <span className="hidden sm:inline">{t('analyzePage.upgradeForUnlimitedScans')}</span>
                            <span className="sm:hidden">{t('analyzePage.upgradeForUnlimitedScansShort')}</span>
                          </Button>
                          <Button
                            disabled
                            className="w-full bg-gray-400 h-14 text-lg opacity-50 cursor-not-allowed"
                            style={{ pointerEvents: 'none' }}
                          >
                            {t('analyzePage.analyzeFoodButton')}
                          </Button>
                        </>
                      );
                    }
                  })()}
                </div>
              )}
            </CardContent>
          </Card>
          {notFoodError && (
            <Alert variant="destructive" className="mt-4 mb-4 w-full max-w-lg">
              <AlertDescription>{notFoodError}</AlertDescription>
            </Alert>
          )}

          {/* Analysis Results Section */}
          {(showAnalysisResultsCard || isAnalyzing) && (
            <Card className={`mt-8 border-2 ${analysisResult?.suitability ? getSuitabilityBorderClass(analysisResult.suitability) : 'border-gray-300'}`}>
              <CardHeader>
                <CardTitle className="text-xl">{t('analyzePage.resultsCardTitle')}</CardTitle>
                <CardDescription className="text-base">{t('analyzePage.resultsCardDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                {isAnalyzing && (
                  <div className="text-center py-12 text-lg">
                    <Loader2 className="h-12 w-12 mx-auto mb-4 animate-spin text-blue-600" />
                    <p className="text-gray-600">{t('analyzePage.analyzingMealMessage')}</p>
                    <Progress value={75} className="mt-4" />

                    {/* Show streaming content if available */}
                    {streamingContent && (
                      <div className="mt-6 p-4 bg-gray-50 rounded-lg text-left">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Live Analysis:</h4>
                        <div className="text-sm text-gray-600 whitespace-pre-wrap font-mono max-h-40 overflow-y-auto">
                          {streamingContent}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {analysisResult && analysisResult.isFood && (
                  <div className="space-y-6">
                    {/* Food Identification */}
                    <div>
                      <h3 className="font-semibold text-xl mb-2">{analysisResult.foodName}</h3>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="text-base">{analysisResult.confidence}% {t('analyzePage.confidence')}</Badge>
                        <Badge className={`${getSuitabilityColor(analysisResult.suitability)} text-base`}>
                          {getSuitabilityIcon(analysisResult.suitability)}
                          <span className="ml-1 capitalize">{t(`suitability.${analysisResult.suitability}`)}</span>
                        </Badge>
                      </div>
                    </div>

                    {/* Nutritional Information */}
                    <div>
                      <h4 className="font-medium mb-3 text-lg">{t('analyzePage.nutritionalBreakdownTitle')}</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-blue-50 p-3 rounded-lg">
                          <div className="text-base text-gray-600">{t('analyzePage.glycemicIndexLabel')}</div>
                          <div className="text-2xl font-bold text-blue-600">{analysisResult.glycemicIndex.min}-{analysisResult.glycemicIndex.max}</div>
                        </div>
                        <div className="bg-green-50 p-3 rounded-lg">
                          <div className="text-base text-gray-600">{t('analyzePage.proteinLabel')}</div>
                          <div className="text-2xl font-bold text-green-600">{analysisResult.protein.min}-{analysisResult.protein.max}g</div>
                        </div>
                        <div className="bg-orange-50 p-3 rounded-lg">
                          <div className="text-base text-gray-600">{t('analyzePage.carbsLabel')}</div>
                          <div className="text-2xl font-bold text-orange-600">{analysisResult.carbs.min}-{analysisResult.carbs.max}g</div>
                        </div>
                        <div className="bg-purple-50 p-3 rounded-lg">
                          <div className="text-base text-gray-600">{t('analyzePage.fiberLabel')}</div>
                          <div className="text-2xl font-bold text-purple-600">{analysisResult.fiber.min}-{analysisResult.fiber.max}g</div>
                        </div>
                      </div>
                    </div>

                    {/* Recommendations */}
                    <div>
                      <h4 className="font-medium mb-3 text-lg">{t('analyzePage.recommendationsTitle')}</h4>
                      <div className="space-y-2">
                        {analysisResult.recommendations.map((rec, index) => (
                          <Alert key={index}>
                            <CheckCircle className="h-5 w-5" />
                            <AlertDescription className="text-base">{rec}</AlertDescription>
                          </Alert>
                        ))}
                      </div>
                    </div>

                    {/* Portion Advice */}
                    <div>
                      <h4 className="font-medium mb-2 text-lg">{t('analyzePage.portionGuidanceTitle')}</h4>
                      <p className="text-base text-gray-600">{analysisResult.portionAdvice}</p>
                    </div>

                    {/* Save Button */}
                    {user && analysisResult && !isSaving && (
                      <Button
                        onClick={handleConfirmSaveClick}
                        disabled={isSaving}
                        className="w-full bg-blue-600 hover:bg-blue-700 h-14 text-lg mt-4"
                      >
                        {t('analyzePage.saveAnalysisButton')}
                      </Button>
                    )}
                    {isSaving && (
                      <Button disabled className="w-full bg-blue-600 hover:bg-blue-700 h-14 text-lg mt-4">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t('analyzePage.savingAnalysisButton')}
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Confirmation Dialog */}
        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t('analyzePage.confirmSaveAnalysisTitle')}</AlertDialogTitle>
              <AlertDialogDescription>
                {t('analyzePage.confirmSaveAnalysisDescription', { foodName: analysisResult?.foodName || t('analyzePage.unknownFood') })}
                <div className="mt-4 space-y-2 text-sm">
                  <div><strong>{t('analyzePage.suitabilityLabel')}:</strong> <Badge className={`${getSuitabilityColor(analysisResult?.suitability || null)}`}>{t(`suitability.${analysisResult?.suitability || 'unknown'}`)}</Badge></div>
                  <div><strong>{t('analyzePage.giLabel')}:</strong> {analysisResult?.glycemicIndex.min}-{analysisResult?.glycemicIndex.max}</div>
                  <div><strong>{t('analyzePage.carbsLabel')}:</strong> {analysisResult?.carbs.min}-{analysisResult?.carbs.max}g</div>
                  <div><strong>{t('analyzePage.proteinLabel')}:</strong> {analysisResult?.protein.min}-{analysisResult?.protein.max}g</div>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t('analyzePage.cancelButton')}</AlertDialogCancel>
              <AlertDialogAction onClick={executeSaveAnalysis}>{t('analyzePage.confirmSaveButton')}</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="fixed bottom-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg flex items-center space-x-2 z-50">
            <CheckCircle className="h-5 w-5" />
            <span>{t('analyzePage.analysisSavedSuccess')}</span>
          </div>
        )}

        {/* Removed: Limit Reached Alert Dialog */}

        {/* Individual Food Item Analysis Section */}
        {analysisResult && analysisResult.isFood && analysisResult.individualItems && analysisResult.individualItems.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="text-xl">{t('analyzePage.individualAnalysisTitle')}</CardTitle>
              <CardDescription className="text-base">{t('analyzePage.individualAnalysisDescription')}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {analysisResult.individualItems.map((item, index) => (
                <div key={index} className="border-b pb-4 last:border-b-0 last:pb-0">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-xl">{item.name}</h3>
                    <Badge className={`${getSuitabilityColor(item.suitability)} text-base`}>
                      {getSuitabilityIcon(item.suitability)}
                      <span className="ml-1 capitalize">{t(`suitability.${item.suitability}`)}</span>
                    </Badge>
                  </div>
                  <p className="text-base text-gray-700 mb-1">
                    <span className="font-medium">{t('analyzePage.contributionLabel')}:</span> {item.contribution}
                  </p>
                  <p className="text-base text-gray-700">
                    <span className="font-medium">{t('analyzePage.concernsLabel')}:</span> {item.concernsForDiabetes}
                  </p>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
