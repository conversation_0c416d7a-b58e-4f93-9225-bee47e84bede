"use client"

export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';

import { TrendingUp, CalendarIcon, Target, Activity, Loader2, Trash2, ChevronLeft, ChevronRight, Pencil, CheckCircle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button, buttonVariants } from "@/components/ui/button"
import { format, subDays, isWithinInterval, addDays, startOfMonth, endOfMonth, subMonths } from "date-fns" // Added date functions for shortcuts
import { formatInTimeZone } from "date-fns-tz"
import { useState, useEffect, useRef } from "react" // Added useRef
import type { DateRange } from "react-day-picker"
import { cn } from "@/lib/utils"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
} from "@/components/ui/pagination"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import { useUser } from '@clerk/nextjs';
import { useClerkSupabase } from '@/lib/supabaseClient';
import { useToast } from "@/components/ui/use-toast";
import { Bar, BarChart, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts"; // Added Bar, BarChart, XAxis, YAxis, Tooltip, ResponsiveContainer
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { useTranslation } from 'react-i18next'; // Import useTranslation
import i18n from '@/lib/i18n'; // Import i18n instance
import { type VariantProps } from "class-variance-authority" // Import VariantProps

interface NutritionalRange {
  min: number;
  max: number;
}

interface FoodAnalysis {
  id: string;
  created_at: string;
  user_id: string;
  analysis_date: string;
  food_name: string;
  suitability: "good" | "moderate" | "caution";
  gi: number;
  carbs_g: number;
  protein_g: number;
  fiber_g?: number;
  components: any[];
  nutritional_ranges?: {
    glycemicIndex?: NutritionalRange;
    carbs?: NutritionalRange;
    protein?: NutritionalRange;
    fiber?: NutritionalRange;
    sugar?: NutritionalRange;
  };
}

export default function DashboardPage() {
  const { t } = useTranslation(); // Initialize useTranslation hook
  const { user } = useUser();
  const [dateFrom, setDateFrom] = useState<Date | undefined>(() => subDays(new Date(), 6));
  const [dateTo, setDateTo] = useState<Date | undefined>(() => new Date());
  const [isDateFromPopoverOpen, setIsDateFromPopoverOpen] = useState(false);
  const [isDateToPopoverOpen, setIsDateToPopoverOpen] = useState(false);
  const [allUserAnalyses, setAllUserAnalyses] = useState<FoodAnalysis[]>([]);
  const [filteredAnalyses, setFilteredAnalyses] = useState<FoodAnalysis[]>([])
  const [showAnalysisTable, setShowAnalysisTable] = useState(false)
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<any>(null); // State for user profile
  const lastFetchedUserId = useRef<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const perPage = Number(process.env.NEXT_PUBLIC_FOOD_ANALYSES_PER_PAGE) || 10;
  const totalPages = Math.max(1, Math.ceil(filteredAnalyses.length / perPage));
  const paginatedAnalyses = filteredAnalyses.slice((currentPage - 1) * perPage, currentPage * perPage);

  // Delete dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Edit dialog state
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingAnalysis, setEditingAnalysis] = useState<FoodAnalysis | null>(null);
  const [newAnalysisDate, setNewAnalysisDate] = useState<Date | undefined>(undefined);
  const [newAnalysisTime, setNewAnalysisTime] = useState<string>('');
  const [editLoading, setEditLoading] = useState(false);
  const [showEditSuccessMessage, setShowEditSuccessMessage] = useState(false);

  // Confirmation dialog state and ref
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const confirmPromise = useRef<{ resolve: (value: boolean) => void } | null>(null);

  const supabase = useClerkSupabase();
  const { toast } = useToast();

  // Food breakdown modal state
  const [breakdownOpen, setBreakdownOpen] = useState(false);
  const [breakdownFood, setBreakdownFood] = useState<FoodAnalysis | null>(null);

  // Helper function to get all dates between two dates
  const getDatesInRange = (startDate: Date, endDate: Date) => {
    const dates = [];
    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));
      currentDate = addDays(currentDate, 1);
    }
    return dates;
  };

  // Prepare data for GI Index Bar Chart
  const giIndexChartData = (() => {
    if (!dateFrom || !dateTo) return [];

    const allDates = getDatesInRange(dateFrom, dateTo);
    const chartDataMap = new Map<string, { date: string; totalGI: number }>();

    // Initialize chart data with all dates and totalGI as 0
    allDates.forEach(date => {
      const formattedDate = format(date, "MMM dd");
      chartDataMap.set(formattedDate, { date: formattedDate, totalGI: 0 });
    });

    // Aggregate GI values from filtered analyses
    filteredAnalyses.forEach(analysis => {
      const formattedDate = format(new Date(analysis.analysis_date), "MMM dd");
      const entry = chartDataMap.get(formattedDate);
      if (entry) {
        entry.totalGI += analysis.gi;
      }
    });

    // Convert map values to array and sort by date
    return Array.from(chartDataMap.values()).sort((a, b) => {
      const dateA = new Date(a.date + ' ' + new Date().getFullYear()).getTime(); // Add current year for proper date comparison
      const dateB = new Date(b.date + ' ' + new Date().getFullYear()).getTime();
      return dateA - dateB;
    });
  })();

    const giIndexChartConfig = {
      totalGI: {
        label: t('dashboardPage.giIndexLabel'),
        color: "hsl(var(--chart-1))",
      },
    } satisfies ChartConfig;

    useEffect(() => {
    const fetchUserData = async () => {
      if (user && supabase && lastFetchedUserId.current !== user.id) {
        setLoading(true);
        lastFetchedUserId.current = user.id;

        // Fetch user profile
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (profileError && profileError.code !== 'PGRST116') { // PGRST116 means no rows found
          console.error('Error fetching user profile:', profileError);
        } else {
          setUserProfile(profileData);
        }

        // Fetch food analyses
        const { data, error } = await supabase
          .from('food_analyses')
          .select('*')
          .eq('user_id', user.id)
          .order('analysis_date', { ascending: false });

        if (error) {
          console.error('Error fetching analyses:', error);
        } else {
          const sortedData = (data || []).sort((a, b) => {
            const dateA = new Date(a.analysis_date).getTime();
            const dateB = new Date(b.analysis_date).getTime();
            if (dateA !== dateB) {
              return dateB - dateA;
            }
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          });
          setAllUserAnalyses(sortedData);
          if (dateFrom && dateTo) {
             const initialFiltered = filterAnalysesByDateRange(sortedData, dateFrom, dateTo);
             setFilteredAnalyses(initialFiltered);
             setShowAnalysisTable(true);
          } else {
             setFilteredAnalyses(sortedData);
             setShowAnalysisTable(true);
          }
        }
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user, supabase]); // Refetch when user or supabase client changes

  const giToleranceScore = userProfile?.gi_tolerance === 'Low' ? 55 :
                           userProfile?.gi_tolerance === 'Medium' ? 65 :
                           userProfile?.gi_tolerance === 'High' ? 75 :
                           60; // Default if not set or unknown

  // Unified filtering function
  const filterAnalysesByDateRange = (analyses: FoodAnalysis[], fromDate: Date, toDate: Date) => {
    const from = new Date(fromDate);
    from.setHours(0, 0, 0, 0);
    const to = new Date(toDate);
    to.setHours(23, 59, 59, 999);

    return analyses.filter((analysis) => {
      const analysisDate = new Date(analysis.analysis_date);
      analysisDate.setHours(12, 0, 0, 0); // Normalize to noon to avoid timezone issues
      return analysisDate >= from && analysisDate <= to;
    }).sort((a, b) => new Date(b.analysis_date).getTime() - new Date(a.analysis_date).getTime());
  };

  // Date shortcut handlers
  const handleDateShortcut = (shortcut: 'last7days' | 'last30days' | 'thisMonth' | 'lastMonth') => {
    console.log('Date shortcut clicked:', shortcut);
    const today = new Date();
    let from: Date;
    let to: Date = today;

    switch (shortcut) {
      case 'last7days':
        from = subDays(today, 6); // Last 7 days including today
        break;
      case 'last30days':
        from = subDays(today, 29); // Last 30 days including today
        break;
      case 'thisMonth':
        from = startOfMonth(today);
        to = endOfMonth(today);
        break;
      case 'lastMonth':
        const lastMonth = subMonths(today, 1);
        from = startOfMonth(lastMonth);
        to = endOfMonth(lastMonth);
        break;
      default:
        return;
    }

    console.log('Date range set:', { from, to, allUserAnalyses: allUserAnalyses.length });
    setDateFrom(from);
    setDateTo(to);

    // Auto-apply the filter using unified function
    const filtered = filterAnalysesByDateRange(allUserAnalyses, from, to);
    console.log('Shortcut filtered results:', filtered.length);
    setFilteredAnalyses(filtered);
    setShowAnalysisTable(true);
    setCurrentPage(1); // Reset to first page
  };

  // Filter handler for Filter button
  const handleFilter = () => {
    console.log('Filter button clicked', { dateFrom, dateTo, allUserAnalyses: allUserAnalyses.length });
    if (dateFrom && dateTo) {
      const filtered = filterAnalysesByDateRange(allUserAnalyses, dateFrom, dateTo);
      console.log('Filtered results:', filtered.length);
      setFilteredAnalyses(filtered);
    } else {
      // If no dates selected, show all analyses sorted by date
      const sorted = [...allUserAnalyses].sort((a, b) => new Date(b.analysis_date).getTime() - new Date(a.analysis_date).getTime());
      console.log('Showing all analyses:', sorted.length);
      setFilteredAnalyses(sorted);
    }
    setCurrentPage(1); // Reset to first page on filter
    setShowAnalysisTable(true);
  };

  // Calculate weekly stats from filteredAnalyses
  const totalAnalyses = filteredAnalyses.length;

  const goodChoices = filteredAnalyses.filter(analysis => analysis.suitability === 'good').length;
  const moderateChoices = filteredAnalyses.filter(analysis => analysis.suitability === 'moderate').length;
  const cautionChoices = filteredAnalyses.filter(analysis => analysis.suitability === 'caution').length;

  const goodChoicesPercentage = totalAnalyses > 0 ? Math.round((goodChoices / totalAnalyses) * 100) : 0;
  const moderateChoicesPercentage = totalAnalyses > 0 ? Math.round((moderateChoices / totalAnalyses) * 100) : 0;
  const cautionChoicesPercentage = totalAnalyses > 0 ? Math.round((cautionChoices / totalAnalyses) * 100) : 0;

  const averageGIScore = totalAnalyses > 0 ? Math.round(filteredAnalyses.reduce((sum, analysis) => sum + analysis.gi, 0) / totalAnalyses) : 0;
  const totalCarbsIntake = filteredAnalyses.reduce((sum, analysis) => sum + analysis.carbs_g, 0);

  const uniqueAnalysisDates = new Set(filteredAnalyses.map(analysis => format(new Date(analysis.analysis_date), "yyyy-MM-dd")));
  const daysWithAnalysis = uniqueAnalysisDates.size;
  const averageCarbsPerMeal = totalAnalyses > 0 ? Math.round(totalCarbsIntake / totalAnalyses) : 0;
  const getSuitabilityColor = (suitability: string) => {
    switch (suitability) {
      case "good":
        return "bg-green-100 text-green-800"
      case "moderate":
        return "bg-yellow-100 text-yellow-800"
      case "caution":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const chartConfig = {
    good: {
      label: t('suitability.good'),
      color: "#166534", // text-green-800
    },
    moderate: {
      label: t('suitability.moderate'),
      color: "#92400e", // text-yellow-800
    },
    caution: {
      label: t('suitability.caution'),
      color: "#991b1b", // text-red-800
    },
  } satisfies ChartConfig;

  const pieChartData = [
    { name: "good", value: goodChoices, color: chartConfig.good.color },
    { name: "moderate", value: moderateChoices, color: chartConfig.moderate.color },
    { name: "caution", value: cautionChoices, color: chartConfig.caution.color },
  ];

  // Delete handler
  const handleDelete = async () => {
    if (!deletingId || !supabase) return;
    setDeleteLoading(true);
    try {
      // Log the deletingId for debugging
      console.log("Attempting to delete food_analyses with id:", deletingId);
      const { error, data } = await supabase
        .from("food_analyses")
        .delete()
        .eq("id", deletingId);

      if (error) {
        console.error("Supabase delete error:", error);
      } else {
        // Refetch food analyses from Supabase to ensure UI is in sync
        if (user) {
          const { data: refreshed, error: fetchError } = await supabase
            .from("food_analyses")
            .select("*")
            .eq("user_id", user.id)
            .order("analysis_date", { ascending: false });
          if (!fetchError && refreshed) {
            setAllUserAnalyses(refreshed);
            // Re-apply filter if needed
            if (dateFrom && dateTo) {
              const filtered = filterAnalysesByDateRange(refreshed, dateFrom, dateTo);
              setFilteredAnalyses(filtered);
            } else {
              const sorted = [...refreshed].sort((a, b) => new Date(b.analysis_date).getTime() - new Date(a.analysis_date).getTime());
              setFilteredAnalyses(sorted);
            }
          }
        }
      }
    } catch (err) {
      console.error("Unexpected error during delete:", err);
    }
    setDeleteLoading(false);
    setDeleteDialogOpen(false);
    setDeletingId(null);
  };

  // Edit handler
  const handleEdit = async () => {
    if (!editingAnalysis || !newAnalysisDate || !newAnalysisTime || !supabase) {
      console.log("Missing data for edit:", { editingAnalysis, newAnalysisDate, newAnalysisTime, supabase });
      toast({
        title: "Missing Data",
        description: "Please select a valid date and time before saving.",
        variant: "destructive",
      });
      return;
    }

    // Show confirmation dialog
    const confirmed = await new Promise<boolean>((resolve) => {
      confirmPromise.current = { resolve };
      setConfirmDialogOpen(true);
    });

    if (!confirmed) {
      console.log("Update cancelled by user.");
      return; // Stop if not confirmed
    }

    setEditLoading(true);
    try {
      const [hours, minutes] = newAnalysisTime.split(':').map(Number);
      const updatedDate = new Date(newAnalysisDate);
      updatedDate.setHours(hours, minutes, 0, 0);

      console.log("Attempting to update analysis:", {
        id: editingAnalysis.id,
        oldDate: editingAnalysis.analysis_date,
        newDate: updatedDate.toISOString(),
      });

      const response = await fetch('/api/update-food-analysis', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingAnalysis.id,
          analysis_date: updatedDate.toISOString(),
        }),
      });

      if (!response.ok) {
        // Read body as text, then try to parse as JSON, fallback to text
        let errorData: any = {};
        const text = await response.text();
        try {
          errorData = JSON.parse(text);
        } catch {
          errorData = { error: text || "Unknown error" };
        }
        console.error('API update error:', JSON.stringify(errorData, null, 2)); // Stringify for detailed logging
        toast({
          title: "Update Failed",
          description: (errorData as any)?.error || "Failed to update analysis date/time.",
          variant: "destructive",
        });
        setEditLoading(false);
        return;
      }

      const result = await response.json();
      console.log('API update success:', result);

      // Refetch food analyses to update UI
      if (user) {
        const { data: refreshed, error: fetchError } = await supabase
          .from("food_analyses")
          .select("*")
          .eq("user_id", user.id)
          .order("analysis_date", { ascending: false });
        if (!fetchError && refreshed) {
          setAllUserAnalyses(refreshed);
          // Re-apply filter if needed
          if (dateFrom && dateTo) {
            const initialFiltered = filterAnalysesByDateRange(refreshed, dateFrom, dateTo);
            setFilteredAnalyses(initialFiltered);
          } else {
            const sorted = [...refreshed].sort((a, b) => new Date(b.analysis_date).getTime() - new Date(a.analysis_date).getTime());
            setFilteredAnalyses(sorted);
          }
        }
      }
      setEditDialogOpen(false);
      setEditingAnalysis(null);
      setNewAnalysisDate(undefined);
      setNewAnalysisTime('');

      // Show success banner
      setShowEditSuccessMessage(true);
      setTimeout(() => {
        setShowEditSuccessMessage(false);
      }, 3000);

      toast({
        title: "Update Successful",
        description: "The analysis date and time have been updated.",
        variant: "default",
      });
    } catch (err: any) {
      console.error('Unexpected error during edit:', err);
      toast({
        title: "Unexpected Error",
        description: err?.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    }
    setEditLoading(false);
  };

  // Reset currentPage to 1 if filteredAnalyses changes and currentPage is out of range
  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filteredAnalyses, totalPages]);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto w-[90%] md:w-[70%] px-4">
        <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('dashboardPage.title')}</h1>
            <p className="text-gray-600">{t('dashboardPage.description')}</p>
          </div>
          <div className="mt-4 md:mt-0 space-y-3">
            {/* Date Shortcuts */}
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm font-medium text-gray-700 mr-2">{t('dashboardPage.quickSelect')}:</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDateShortcut('last7days')}
                className="text-xs"
              >
                {t('dashboardPage.last7Days')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDateShortcut('last30days')}
                className="text-xs"
              >
                {t('dashboardPage.last30Days')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDateShortcut('thisMonth')}
                className="text-xs"
              >
                {t('dashboardPage.thisMonth')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDateShortcut('lastMonth')}
                className="text-xs"
              >
                {t('dashboardPage.lastMonth')}
              </Button>
            </div>

            {/* Date Range Picker */}
            <div className="flex flex-wrap items-center gap-2">
            <Popover open={isDateFromPopoverOpen} onOpenChange={setIsDateFromPopoverOpen}>
              <PopoverTrigger asChild>
                <Button
                  id="date-from"
                  variant={"outline"}
                  className={cn(
                    "w-full md:w-auto justify-start text-left font-normal",
                    !dateFrom && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateFrom ? format(dateFrom, "LLL dd, y") : <span>{t('dashboardPage.dateFrom')}</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={dateFrom}
                  onSelect={(date) => {
                    setDateFrom(date);
                    setIsDateFromPopoverOpen(false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <Popover open={isDateToPopoverOpen} onOpenChange={setIsDateToPopoverOpen}>
              <PopoverTrigger asChild>
                <Button
                  id="date-to"
                  variant={"outline"}
                  className={cn(
                    "w-full md:w-auto justify-start text-left font-normal",
                    !dateTo && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateTo ? format(dateTo, "LLL dd, y") : <span>{t('dashboardPage.dateTo')}</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={dateTo}
                  onSelect={(date) => {
                    setDateTo(date);
                    setIsDateToPopoverOpen(false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <Button
              onClick={handleFilter}
              className="bg-blue-600 hover:bg-blue-700 w-full md:w-auto"
            >
              {t('dashboardPage.filterButton')}
            </Button>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('dashboardPage.daysWithAnalysisLabel')}</CardTitle>
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{daysWithAnalysis}</div>
              <p className="text-xs text-muted-foreground">{t('dashboardPage.daysLabel')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('dashboardPage.totalAnalysesTitle')}</CardTitle>
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalAnalyses}</div>
              <p className="text-xs text-muted-foreground">{t('dashboardPage.mealsAnalyzed')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('dashboardPage.goodChoicesTitle')}</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{goodChoices} ({goodChoicesPercentage}%)</div>
              <p className="text-xs text-muted-foreground">{t('dashboardPage.diabetesFriendlyMeals')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('dashboardPage.moderateChoicesTitle')}</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{moderateChoices} ({moderateChoicesPercentage}%)</div>
              <p className="text-xs text-muted-foreground">{t('dashboardPage.mealsNeedingAttention')}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('dashboardPage.cautionChoicesTitle')}</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{cautionChoices} ({cautionChoicesPercentage}%)</div>
              <p className="text-xs text-muted-foreground">{t('dashboardPage.mealsToBeCautiousOf')}</p>
            </CardContent>
          </Card>
        </div>

        {/* GI Index Bar Chart */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>{t('dashboardPage.giIndexTimelineTitle')}</CardTitle>
            <CardDescription>{t('dashboardPage.giIndexTimelineDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={giIndexChartConfig} className="min-h-[200px] w-full">
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={giIndexChartData}>
                  <XAxis
                    dataKey="date"
                    tickLine={false}
                    tickMargin={10}
                    axisLine={false}
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={(value) => `${value}`}
                  />
                  <Tooltip content={<ChartTooltipContent />} />
                  <Bar dataKey="totalGI" fill="#78787d" radius={4} />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Food Analysis Distribution Pie Chart */}
          <div className="lg:col-span-1">
            <Card className="flex flex-col items-center justify-center p-6 h-full">
              <CardHeader className="text-center">
                <CardTitle>{t('dashboardPage.foodChoicesDistributionTitle')}</CardTitle>
                <CardDescription>{t('dashboardPage.foodChoicesDistributionDescription')}</CardDescription>
              </CardHeader>
              <CardContent className="flex-1 pb-0 flex items-center justify-center">
                {/* Custom SVG Donut Chart with Tailwind */}
                <div className="relative w-60 h-60 flex items-center justify-center">
                  {/* SVG Donut */}
                  <svg width="240" height="240" viewBox="0 0 240 240" className="block">
                    {(() => {
                      // Pie chart math
                      const radius = 100;
                      const thickness = 32;
                      const center = 120;
                      const total = goodChoices + moderateChoices + cautionChoices;
                      const data = [
                        { value: goodChoices, color: "#16a349" },
                        { value: moderateChoices, color: "#ca8a03" },
                        { value: cautionChoices, color: "#dc2625" },
                      ];
                      let startAngle = 0;
                      // For percentage labels
                      const percentLabels: React.ReactNode[] = [];
                      const segments = data.map((d, i) => {
                        const angle = total > 0 ? (d.value / total) * 360 : 0;
                        const endAngle = startAngle + angle;
                        // Arc math
                        const start = (startAngle - 90) * (Math.PI / 180);
                        const end = (endAngle - 90) * (Math.PI / 180);
                        // Arc points
                        const x1 = center + radius * Math.cos(start);
                        const y1 = center + radius * Math.sin(start);
                        const x2 = center + radius * Math.cos(end);
                        const y2 = center + radius * Math.sin(end);
                        // Large arc flag
                        const largeArc = angle > 180 ? 1 : 0;
                        // Only render if value > 0
                        const strokeDasharray = total > 0 ? `${(angle / 360) * 628} 628` : "0 628";
                        const strokeDashoffset = total > 0 ? (628 * startAngle) / 360 : 0;
                        const segment = (
                          <circle
                            key={i}
                            r={radius}
                            cx={center}
                            cy={center}
                            fill="none"
                            strokeWidth={thickness}
                            stroke={d.color}
                            strokeDasharray={strokeDasharray}
                            strokeDashoffset={-strokeDashoffset}
                            strokeLinecap="round"
                          />
                        );
                        // Value label - Removed as per user request
                        // if (d.value > 0) {
                        //   const midAngle = startAngle + angle / 2;
                        //   const midRad = (midAngle - 90) * (Math.PI / 180);
                        //   const labelRadius = radius + thickness / 2 + 25; // Adjusted for better positioning
                        //   const lx = center + labelRadius * Math.cos(midRad);
                        //   const ly = center + labelRadius * Math.sin(midRad);
                        //   percentLabels.push(
                        //     <text
                        //       key={`label-${i}`}
                        //       x={lx}
                        //       y={ly}
                        //       textAnchor="middle"
                        //       dominantBaseline="middle"
                        //       className="text-base font-semibold"
                        //       fill="black" // Changed to black for better visibility
                        //     >
                        //       {d.value}
                        //     </text>
                        //   );
                        // }
                        startAngle = endAngle;
                        return segment;
                      });
                      return (
                        <>
                          {segments}
                          {/* Donut hole */}
                          <circle
                            r="68"
                            cx="120"
                            cy="120"
                            fill="white"
                          />
                          {/* Value labels - Removed as per user request */}
                          {/* {percentLabels} */}
                        </>
                      );
                    })()}
                  </svg>
                  {/* Center text */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
                    <span className="text-3xl font-bold text-gray-900">{totalAnalyses}</span>
                    <span className="text-gray-500 text-sm">{t('dashboardPage.analysesLabel')}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Timeline Insights */}
          <div className="space-y-6 lg:col-span-1">
            <Card className="h-full">
              <CardHeader>
                <CardTitle>{t('dashboardPage.timelineInsightsTitle')}</CardTitle>
                <CardDescription>{t('dashboardPage.timelineInsightsDescription')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>{t('dashboardPage.averageGiScoreTitle')}</span>
                    <span className={averageGIScore > giToleranceScore ? "text-red-600 font-bold" : ""}>
                      {averageGIScore}/{giToleranceScore}
                    </span>
                  </div>
                  <Progress
                    value={averageGIScore > giToleranceScore ? 100 : (averageGIScore / giToleranceScore) * 100}
                    indicatorClassName={averageGIScore > giToleranceScore ? "bg-red-500" : ""}
                  />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>{t('dashboardPage.averageCarbsPerMealLabel')}</span>
                    <span>{averageCarbsPerMeal} {t('dashboardPage.gramsLabel')}</span>
                  </div>
                  <Progress value={(averageCarbsPerMeal / 200) * 100} /> {/* Placeholder: Assuming a target of 200g carbs for progress bar */}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Filtered Food Analysis Table */}
        {showAnalysisTable && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>{t('dashboardPage.filteredAnalysesTitle')}</CardTitle>
              <CardDescription>
                {t('dashboardPage.showingAnalysesFrom')} {dateFrom ? format(dateFrom, "LLL dd, yyyy") : t('dashboardPage.start')} {t('dashboardPage.to')} {" "}
                {dateTo ? format(dateTo, "LLL dd, yyyy") : t('dashboardPage.end')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                 <div className="text-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
                    <p className="text-gray-600 mt-2">{t('dashboardPage.loadingFilteredAnalyses')}</p>
                 </div>
              ) : filteredAnalyses.length > 0 ? (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                          <TableHead>{t('dashboardPage.tableHeadDate')} & Time</TableHead>
                        <TableHead>{t('dashboardPage.tableHeadFoodName')}</TableHead>
                        <TableHead>{t('dashboardPage.tableHeadSuitability')}</TableHead>
                        <TableHead>{t('dashboardPage.tableHeadGi')}</TableHead>
                        <TableHead>{t('dashboardPage.tableHeadCarbs')}</TableHead>
                          <TableHead>{t('dashboardPage.tableHeadProtein')}</TableHead>
                          <TableHead>{t('dashboardPage.tableHeadFiber')}</TableHead>
                          <TableHead className="text-right">{t('dashboardPage.tableHeadActions')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedAnalyses.map((analysis) => (
                        <TableRow key={analysis.id}>
                          <TableCell>
                            {formatInTimeZone(analysis.analysis_date, userProfile?.timezone || 'UTC', "dd/MMM/yyyy hh:mma")}
                          </TableCell>
                          <TableCell className="font-medium">
                            <button
                              className="text-blue-700 underline hover:text-blue-900"
                              onClick={() => {
                                setBreakdownFood(analysis);
                                setBreakdownOpen(true);
                              }}
                              style={{ background: "none", border: "none", padding: 0, margin: 0, cursor: "pointer" }}
                            >
                              {analysis.food_name}
                            </button>
                          </TableCell>
                          <TableCell>
                            <Badge variant="default" className={getSuitabilityColor(analysis.suitability)}>
                              {t(`suitability.${analysis.suitability}`)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {analysis.nutritional_ranges?.glycemicIndex
                              ? `${analysis.nutritional_ranges.glycemicIndex.min}-${analysis.nutritional_ranges.glycemicIndex.max}`
                              : analysis.gi}
                          </TableCell>
                          <TableCell>
                            {analysis.nutritional_ranges?.carbs
                              ? `${analysis.nutritional_ranges.carbs.min}-${analysis.nutritional_ranges.carbs.max}g`
                              : `${analysis.carbs_g}g`}
                          </TableCell>
                          <TableCell>
                            {analysis.nutritional_ranges?.protein
                              ? `${analysis.nutritional_ranges.protein.min}-${analysis.nutritional_ranges.protein.max}g`
                              : `${analysis.protein_g}g`}
                          </TableCell>
                          <TableCell>
                            {analysis.nutritional_ranges?.fiber
                              ? `${analysis.nutritional_ranges.fiber.min}-${analysis.nutritional_ranges.fiber.max}g`
                              : analysis.fiber_g ? `${analysis.fiber_g}g` : 'N/A'}
                          </TableCell>
                          <TableCell className="text-right flex items-center justify-end space-x-2">
                            <button
                              className="text-gray-600 hover:text-gray-800"
                              onClick={() => {
                                setEditingAnalysis(analysis);
                                setNewAnalysisDate(new Date(analysis.analysis_date));
                                setNewAnalysisTime(format(new Date(analysis.analysis_date), 'HH:mm'));
                                setEditDialogOpen(true);
                              }}
                              aria-label="Edit"
                            >
                              <Pencil className="w-5 h-5" />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-800"
                              onClick={() => {
                                setDeletingId(analysis.id);
                                setDeleteDialogOpen(true);
                              }}
                              aria-label="Delete"
                              disabled={deleteLoading}
                            >
                              <Trash2 className="w-5 h-5" />
                            </button>
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  </Table>
  {/* Pagination Controls */}
{totalPages > 1 && (
  <div className="mt-4 flex justify-center">
    <Pagination>
      <PaginationContent>
        <PaginationItem>
          <button
            type="button"
            aria-label="Go to previous page"
            onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
            disabled={currentPage === 1}
            className={buttonVariants({
              variant: "ghost",
              size: "default",
            }) + " gap-1 pl-2.5" + (currentPage === 1 ? " opacity-50 cursor-not-allowed" : "")}
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Previous</span>
          </button>
        </PaginationItem>
        {/* Page numbers */}
        {Array.from({ length: totalPages }).map((_, idx) => {
          // Show first, last, current, and neighbors; ellipsis for gaps
          const page = idx + 1;
          const isCurrent = page === currentPage;
          const isEdge = page === 1 || page === totalPages;
          const isNear = Math.abs(page - currentPage) <= 1;
          if (isEdge || isNear) {
            return (
              <PaginationItem key={page}>
                <button
                  type="button"
                  aria-current={isCurrent ? "page" : undefined}
                  onClick={() => setCurrentPage(page)}
                  className={buttonVariants({
                    variant: isCurrent ? "outline" : "ghost",
                    size: "icon",
                  }) + (isCurrent ? " border-primary" : "")}
                  style={{ minWidth: 36 }}
                >
                  {page}
                </button>
              </PaginationItem>
            );
          }
          // Ellipsis logic
          if (
            (page === currentPage - 2 && page > 1) ||
            (page === currentPage + 2 && page < totalPages)
          ) {
            return (
              <PaginationItem key={page + "-ellipsis"}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }
          return null;
        })}
        <PaginationItem>
          <button
            type="button"
            aria-label="Go to next page"
            onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
            className={buttonVariants({
              variant: "ghost",
              size: "default",
            }) + " gap-1 pr-2.5" + (currentPage === totalPages ? " opacity-50 cursor-not-allowed" : "")}
          >
            <span>Next</span>
            <ChevronRight className="h-4 w-4" />
          </button>
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  </div>
)}
                </>
              ) : (
                <p className="text-center text-gray-500 py-8">{t('dashboardPage.noAnalysesFound')}</p>
              )}
            </CardContent>
          </Card>
        )}
        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this food analysis record? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={deleteLoading}>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete} disabled={deleteLoading}>
                {deleteLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Edit Analysis Dialog */}
        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>{t('dashboardPage.editAnalysisTitle')}</DialogTitle>
              <DialogDescription>
                {t('dashboardPage.editAnalysisDescription')}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="date" className="text-right">
                  {t('dashboardPage.dateLabel')}
                </label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "col-span-3 justify-start text-left font-normal",
                        !newAnalysisDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newAnalysisDate ? format(newAnalysisDate, "PPP") : <span>{t('dashboardPage.pickADate')}</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={newAnalysisDate}
                      onSelect={setNewAnalysisDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="time" className="text-right">
                  {t('dashboardPage.timeLabel')}
                </label>
                <input
                  id="time"
                  type="time"
                  value={newAnalysisTime}
                  onChange={(e) => setNewAnalysisTime(e.target.value)}
                  className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditDialogOpen(false)} disabled={editLoading}>
                {t('dashboardPage.cancelButton')}
              </Button>
              <Button onClick={handleEdit} disabled={editLoading}>
                {editLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                {t('dashboardPage.saveChangesButton')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Confirmation Dialog */}
        <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Changes</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to save the changes to this food analysis record's date and time?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => {
                confirmPromise.current?.resolve(false);
                setConfirmDialogOpen(false);
              }}>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={() => {
                confirmPromise.current?.resolve(true);
                setConfirmDialogOpen(false);
              }}>Confirm</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

      </div>
      {/* Food Breakdown Dialog */}
      <Dialog open={breakdownOpen} onOpenChange={setBreakdownOpen}>
        <DialogContent className="max-w-4xl w-full" style={{ maxHeight: "90vh" }}>
          <DialogHeader>
            <DialogTitle>{t('dashboardPage.foodComponentsBreakdownTitle')}</DialogTitle>
            <DialogDescription>
              {breakdownFood?.food_name ? (
                <span>
                  {t('dashboardPage.componentsFor')} <span className="font-semibold">{breakdownFood.food_name}</span>
                </span>
              ) : null}
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-auto">
            {breakdownFood?.components && Array.isArray(breakdownFood.components) && breakdownFood.components.length > 0 ? (
              // Try to render as table if array of objects with same keys
              (() => {
                const keys =
                  breakdownFood.components.every(
                    (c) => typeof c === "object" && c !== null && !Array.isArray(c)
                  )
                    ? Array.from(
                        new Set(
                          breakdownFood.components.flatMap((c) => Object.keys(c))
                        )
                      )
                    : null;
                if (keys) {
                  return (
                    <table className="min-w-full text-sm border">
                      <thead>
                        <tr>
                          {keys.map((k) => (
                            <th key={k} className="border px-2 py-1 text-left bg-gray-100">
                              {t(`dashboardPage.breakdownTableHeaders.${k}`)}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {breakdownFood.components.map((c, i) => (
                          <tr key={i}>
                            {keys.map((k) => (
                              <td key={k} className="border px-2 py-1">
                                {k === "suitability" && typeof c[k] === "string" ? (
                                  <Badge variant="default" className={getSuitabilityColor(c[k])}>
                                    {t(`suitability.${c[k]}`)}
                                  </Badge>
                                ) : typeof c[k] === "object"
                                  ? JSON.stringify(c[k])
                                  : String(c[k] ?? "")}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  );
                } else {
                  // Fallback: show as JSON
                  return (
                    <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                      {JSON.stringify(breakdownFood.components, null, 2)}
                    </pre>
                  );
                }
              })()
            ) : (
              <span className="text-gray-500">No components data available.</span>
            )}
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Close</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Success Message */}
      {showEditSuccessMessage && (
        <div className="fixed bottom-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg flex items-center space-x-2 z-50">
          <CheckCircle className="h-5 w-5" />
          <span>Analysis saved successfully!</span>
        </div>
      )}
    </div>
  )
}
