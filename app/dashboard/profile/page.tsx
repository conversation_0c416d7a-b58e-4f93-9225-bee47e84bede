"use client";
export const dynamic = "force-dynamic";
export const fetchCache = 'force-no-store';

import { useState, useEffect, useMemo, useRef } from "react"
import { Heart, Loader2, CheckCircle, DollarSign, CreditCard } from "lucide-react" // Added DollarSign
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Command, CommandInput, CommandList, CommandEmpty, CommandItem } from "@/components/ui/command"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"
import { useClerkSupabase } from "@/lib/supabaseClient"
import { useSession } from "@clerk/nextjs"
import PricingDeck from "@/components/pricing/PricingDeck"
import { useSubscription } from "@/hooks/useSubscription"
import { useTranslation } from 'react-i18next'; // Import useTranslation
import i18n from '@/lib/i18n'; // Import i18n instance
import { useIsMobile } from "@/hooks/use-mobile";
import { differenceInDays } from 'date-fns';

export default function ProfilePage() {
  const { t } = useTranslation(); // Initialize useTranslation hook
  const [timezoneSearch, setTimezoneSearch] = useState(""); // State for timezone search input
  const [isTimezoneSelectOpen, setIsTimezoneSelectOpen] = useState(false); // State to control Select open/close (for desktop)
  const [showTimezoneDialog, setShowTimezoneDialog] = useState(false); // State to control Dialog open/close (for mobile)
  const timezoneInputRef = useRef<HTMLInputElement>(null); // Ref for the CommandInput

  const isMobile = useIsMobile();

  const allTimezones = useMemo(() => {
    if (typeof Intl === 'undefined' || typeof Intl.supportedValuesOf === 'undefined') {
      // Fallback for environments where Intl.supportedValuesOf is not available
      return ["America/New_York", "America/Los_Angeles", "Europe/London", "Asia/Singapore"];
    }
    return Intl.supportedValuesOf('timeZone').sort();
  }, []);

  const filteredTimezones = useMemo(() => {
    return allTimezones.filter(tz =>
      tz.toLowerCase().includes(timezoneSearch.toLowerCase())
    );
  }, [allTimezones, timezoneSearch]);

  const [profile, setProfile] = useState<{
    timezone: string;
    language: string;
    giTolerance: string;
    trialStartDate: Date | null;
    trialEndDate: Date | null;
    isTrialActive: boolean;
    hasTrialEverBeenActive: boolean;
  }>({
    timezone: "Asia/Singapore",
    language: "en", // Store language key
    giTolerance: "Medium",
    trialStartDate: null,
    trialEndDate: null,
    isTrialActive: false,
    hasTrialEverBeenActive: false,
  })

  const supabase = useClerkSupabase();
  const { session } = useSession();
  const { subscription, loading: subscriptionLoading } = useSubscription();
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const handleManageBilling = async () => {
    try {
      const response = await fetch('/api/stripe/customer-portal', {
        method: 'POST',
      });

      if (response.ok) {
        const { url } = await response.json();
        window.open(url, '_blank');
      } else {
        console.error('Failed to create customer portal session');
      }
    } catch (error) {
      console.error('Error opening customer portal:', error);
    }
  };

  useEffect(() => {
    const fetchProfile = async () => {
      if (!supabase || !session || !session.user) {
        return;
      }

      const clerkUserId = session.user.id;
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*, trial_start_date, trial_end_date, is_trial_active, has_trial_ever_been_active')
        .eq('user_id', clerkUserId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found
        console.error("Error fetching profile:", error);
      } else if (data) {
        setProfile({
          timezone: data.timezone || "Asia/Singapore",
          language: data.language || "en", // Use language key
          giTolerance: data.gi_tolerance || "Medium",
          trialStartDate: data.trial_start_date ? new Date(data.trial_start_date) : null,
          trialEndDate: data.trial_end_date ? new Date(data.trial_end_date) : null,
          isTrialActive: data.is_trial_active,
          hasTrialEverBeenActive: data.has_trial_ever_been_active,
        });
        i18n.changeLanguage(data.language || 'en'); // Change i18n language
      }
    };

    fetchProfile();
  }, [supabase, session]);

  const executeSaveProfile = async () => {
    if (!supabase) {
      console.error("Supabase client not initialized.");
      return;
    }

    if (!session || !session.user) {
      console.error("Clerk session or user not found.");
      return;
    }

    setIsSaving(true);
    setShowConfirmDialog(false); // Close dialog immediately

    const clerkUserId = session.user.id;

    const profileData = {
      user_id: clerkUserId,
      timezone: profile.timezone,
      language: profile.language, // Save language key
      gi_tolerance: profile.giTolerance,
    };

    console.log("Attempting to save profile data:", profileData);

    const { data, error } = await supabase
      .from('user_profiles')
      .upsert(profileData, { onConflict: 'user_id' });

    if (error) {
      console.error("Error saving profile:", error);
      // Log the full error object for detailed debugging
      console.error("Supabase error object:", JSON.stringify(error, null, 2));
    } else {
      console.log("Profile saved successfully:", data);
      setShowSuccessMessage(true);
      setTimeout(() => {
        setShowSuccessMessage(false);
      }, 3000);
      i18n.changeLanguage(profile.language); // Change i18n language after successful save
    }
    setIsSaving(false);
  }

  const handleSaveClick = () => {
    setShowConfirmDialog(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto w-[90%] md:w-[70%] px-4">
        <div className="mb-8 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('profileSettings.title')}</h1>
            <p className="text-gray-600">{t('profileSettings.description')}</p>
          </div>

          {/* Subscription Status in Header */}
          {!subscriptionLoading && (
            <div className="lg:min-w-[300px]">
              {subscription.isTrialActive && subscription.trialEndDate && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-orange-800">{t('profileSettings.freeTrialActive')}</h4>
                        <p className="text-sm text-orange-600">
                          {(() => {
                            const daysLeft = differenceInDays(subscription.trialEndDate, new Date());
                            return daysLeft > 0
                              ? t('profileSettings.daysRemaining', { days: daysLeft })
                              : t('profileSettings.trialEndingToday');
                          })()}
                        </p>
                      </div>
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                        {t('profileSettings.trial')}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              )}

              {subscription.isActive && (
                <Card className="border-green-200 bg-green-50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-green-800">
                          {subscription.planType === 'starter' ? t('profileSettings.starterPlan') :
                           subscription.planType === 'unlimited' ? t('profileSettings.unlimitedPlan') : t('profileSettings.activePlan')}
                        </h4>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        {t('profileSettings.active')}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              )}

              {!subscription.isActive && !subscription.isTrialActive && (
                <Card className="border-red-200 bg-red-50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-red-800">No Active Plan</h4>
                        <p className="text-sm text-red-600">Subscribe to continue using features</p>
                      </div>
                      <Badge variant="outline" className="border-red-200 text-red-800">
                        Inactive
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
        {/* Onboarding Nudge Banner */}
        <div className="mb-6">
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="flex items-center gap-4 py-4">
              <CheckCircle className="text-blue-600 w-6 h-6 flex-shrink-0" />
              <div>
                <div className="font-semibold text-blue-800 text-lg">{t('onboarding.completeProfileTitle')}</div>
                <div className="text-blue-700 text-sm">
                  {t('onboarding.completeProfileDescription')}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Navigation */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardContent className="p-4">
                <nav className="space-y-1">
                  <a
                    href="#preferences"
                    className="flex items-center space-x-2 p-2 rounded-lg transition-colors text-gray-600 hover:bg-gray-50 hover:text-blue-700 text-sm"
                  >
                    <Heart className="h-4 w-4" />
                    <span>{t('profileSettings.preferencesNav')}</span>
                  </a>
                  <a
                    href="#subscription"
                    className="flex items-center space-x-2 p-2 rounded-lg transition-colors text-gray-600 hover:bg-gray-50 hover:text-blue-700 text-sm"
                  >
                    <CreditCard className="h-4 w-4" />
                    <span>{t('profileSettings.subscriptionNav')}</span>
                  </a>
                  <a
                    href="#pricing"
                    className="flex items-center space-x-2 p-2 rounded-lg transition-colors text-gray-600 hover:bg-gray-50 hover:text-blue-700 text-sm"
                  >
                    <DollarSign className="h-4 w-4" />
                    <span>{t('profileSettings.pricingNav')}</span>
                  </a>
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Preferences */}
            <Card id="preferences">
              <CardHeader>
                <CardTitle>{t('profileSettings.preferencesTitle')}</CardTitle>
                <CardDescription>{t('profileSettings.preferencesDescription')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4"> {/* Changed from grid to space-y-4 for vertical stacking */}
                  <div className="space-y-2"> {/* Added space-y-2 for label and select */}
                    <Label htmlFor="timezone">{t('profileSettings.timezoneLabel')}</Label>
                    {isMobile ? (
                      <>
                        <Button
                          variant="outline"
                          className="w-full justify-start"
                          onClick={() => setShowTimezoneDialog(true)}
                        >
                          {profile.timezone || t('profileSettings.selectTimezonePlaceholder')}
                        </Button>
                        <Dialog open={showTimezoneDialog} onOpenChange={setShowTimezoneDialog}>
                          <DialogContent className="p-0">
                            <DialogHeader className="px-6 pt-6">
                              <DialogTitle>{t('profileSettings.selectTimezonePlaceholder')}</DialogTitle>
                              <DialogDescription>{t('profileSettings.searchTimezonePlaceholder')}</DialogDescription>
                            </DialogHeader>
                            <Command className="relative rounded-lg border shadow-md">
                              <CommandInput
                                ref={timezoneInputRef}
                                placeholder={t('profileSettings.searchTimezonePlaceholder')}
                                value={timezoneSearch}
                                onValueChange={setTimezoneSearch}
                              />
                              <CommandList className="max-h-[300px] overflow-y-auto">
                                <CommandEmpty>{t('profileSettings.noTimezoneFound')}</CommandEmpty>
                                {filteredTimezones.map((tz) => (
                                  <CommandItem
                                    key={tz}
                                    value={tz}
                                    onSelect={(value) => {
                                      setProfile({ ...profile, timezone: value });
                                      setTimezoneSearch("");
                                      setShowTimezoneDialog(false); // Close dialog after selection
                                    }}
                                  >
                                    {t(`timezones.${tz}`, tz)}
                                  </CommandItem>
                                ))}
                              </CommandList>
                            </Command>
                            <DialogFooter className="px-6 pb-6">
                              <DialogClose asChild>
                                <Button variant="outline">{t('profileSettings.cancelButton')}</Button>
                              </DialogClose>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </>
                    ) : (
                      <Select
                        value={profile.timezone}
                        onValueChange={(value) => {
                          setProfile({ ...profile, timezone: value });
                          setIsTimezoneSelectOpen(false); // Close select on value change
                        }}
                        open={isTimezoneSelectOpen}
                        onOpenChange={(open) => {
                          setIsTimezoneSelectOpen(open);
                          if (open) {
                            // Focus the input when the select opens
                            setTimeout(() => {
                              timezoneInputRef.current?.focus();
                            }, 100); // Small delay to ensure the input is rendered and ready
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue>{profile.timezone || t('profileSettings.selectTimezonePlaceholder')}</SelectValue>
                        </SelectTrigger>
                        <SelectContent className="p-0"> {/* Added p-0 to remove padding */}
                          <Command>
                            <CommandInput
                              ref={timezoneInputRef} // Attach the ref here
                              placeholder={t('profileSettings.searchTimezonePlaceholder')}
                              value={timezoneSearch}
                              onValueChange={setTimezoneSearch}
                            />
                            <CommandList>
                              <CommandEmpty>{t('profileSettings.noTimezoneFound')}</CommandEmpty>
                              {filteredTimezones.map((tz) => (
                                <CommandItem
                                  key={tz}
                                  value={tz}
                                  onSelect={(value) => {
                                    setProfile({ ...profile, timezone: value });
                                    setTimezoneSearch(""); // Clear search after selection
                                    setIsTimezoneSelectOpen(false); // Close select after selection
                                  }}
                                >
                                  {t(`timezones.${tz}`, tz)} {/* Fallback to raw timezone if translation not found */}
                                </CommandItem>
                              ))}
                            </CommandList>
                          </Command>
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                  <div className="space-y-2"> {/* Added space-y-2 for label and select */}
                    <Label htmlFor="language">{t('profileSettings.languageLabel')}</Label>
                    <Select
                      value={profile.language}
                      onValueChange={(value) => setProfile({ ...profile, language: value })}
                    >
                      <SelectTrigger>
                      <SelectValue asChild>
                        <span>{t(`languages.${profile.language}`)}</span>
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="zh">Chinese</SelectItem>
                    </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2"> {/* Added space-y-2 for label and select */}
                  <Label htmlFor="giTolerance">{t('profileSettings.giToleranceLabel')}</Label>
                  <Select
                    value={profile.giTolerance}
                    onValueChange={(value) => setProfile({ ...profile, giTolerance: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('profileSettings.selectGiTolerancePlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">{t('giTolerance.Low')}</SelectItem>
                      <SelectItem value="Medium">{t('giTolerance.Medium')}</SelectItem>
                      <SelectItem value="High">{t('giTolerance.High')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Save Button - Moved inside Preferences Card */}
                <div className="flex justify-end pt-4 border-t">
                  {isSaving ? (
                    <Button disabled className="bg-blue-600 hover:bg-blue-700">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('profileSettings.savingButton')}
                    </Button>
                  ) : (
                    <Button onClick={handleSaveClick} className="bg-blue-600 hover:bg-blue-700">
                      {t('profileSettings.saveChangesButton')}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Subscription Management */}
            <Card id="subscription">
              <CardHeader>
                <CardTitle>{t('profileSettings.subscriptionTitle')}</CardTitle>
                <CardDescription>{t('profileSettings.subscriptionDescription')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {subscription.isActive && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                      <div>
                        <h4 className="font-semibold text-green-800">
                          {subscription.planType === 'starter' ? t('profileSettings.starterPlan') :
                           subscription.planType === 'unlimited' ? t('profileSettings.unlimitedPlan') : t('profileSettings.activePlan')}
                        </h4>

                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        {t('profileSettings.active')}
                      </Badge>
                    </div>

                    <div className="space-y-3">
                      <Button
                        variant="outline"
                        onClick={handleManageBilling}
                        className="w-full"
                      >
                        {t('profileSettings.manageBilling')}
                      </Button>
                    </div>

                    <div className="pt-4 border-t">
                      <Button
                        variant="destructive"
                        onClick={handleManageBilling}
                        className="w-full"
                      >
                        {t('profileSettings.cancelSubscription')}
                      </Button>
                      <p className="text-xs text-gray-500 mt-2 text-center">
                        {t('profileSettings.cancelNote')}
                      </p>
                    </div>
                  </div>
                )}

                {subscription.isTrialActive && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-orange-50 rounded-lg border border-orange-200">
                      <div>
                        <h4 className="font-semibold text-orange-800">Free Trial Active</h4>
                        <p className="text-sm text-orange-600">
                          {subscription.trialEndDate && (
                            `Trial ends: ${new Date(subscription.trialEndDate).toLocaleDateString()}`
                          )}
                        </p>
                      </div>
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                        Trial
                      </Badge>
                    </div>

                    <p className="text-sm text-gray-600">
                      Upgrade to a paid plan to continue using all features after your trial ends.
                    </p>
                  </div>
                )}

                {!subscription.isActive && !subscription.isTrialActive && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200">
                      <div>
                        <h4 className="font-semibold text-red-800">No Active Subscription</h4>
                        <p className="text-sm text-red-600">Subscribe to access all features</p>
                      </div>
                      <Badge variant="outline" className="border-red-200 text-red-800">
                        Inactive
                      </Badge>
                    </div>

                    <p className="text-sm text-gray-600">
                      Choose a plan below to get started with unlimited food analysis.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Pricing Section */}
            <Card id="pricing">
              <CardHeader>
                <CardTitle>{t('profileSettings.upgradeTitle')}</CardTitle>
                <CardDescription>{t('profileSettings.upgradeDescription')}</CardDescription>
              </CardHeader>
              <CardContent>
                <PricingDeck
                  showForLoggedInUser={true}
                  currentPlanId={subscription.planType}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('profileSettings.confirmSaveTitle')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('profileSettings.confirmSaveDescription')}
              <div className="mt-4 space-y-2 text-sm">
                <div><strong>{t('profileSettings.timezoneLabel')}:</strong> {profile.timezone}</div>
                <div><strong>{t('profileSettings.languageLabel')}:</strong> {t(`languages.${profile.language}`)}</div>
                <div><strong>{t('profileSettings.giToleranceLabel')}:</strong> {t(`giTolerance.${profile.giTolerance}`)}</div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('profileSettings.cancelButton')}</AlertDialogCancel>
            <AlertDialogAction onClick={executeSaveProfile}>{t('profileSettings.confirmSaveButton')}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Message */}
      {showSuccessMessage && (
        <div className="fixed bottom-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg flex items-center space-x-2 z-50">
          <CheckCircle className="h-5 w-5" />
          <span>{t('profileSettings.preferencesSavedSuccess')}</span>
        </div>
      )}
    </div>
  )
}
