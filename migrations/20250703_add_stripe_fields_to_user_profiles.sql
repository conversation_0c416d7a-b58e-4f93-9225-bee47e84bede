-- Add Stripe-related fields to user_profiles table for subscription management
ALTER TABLE user_profiles 
ADD COLUMN stripe_customer_id text,
ADD COLUMN stripe_subscription_id text,
ADD COLUMN stripe_subscription_status text,
ADD COLUMN stripe_current_period_end timestamp with time zone,
ADD COLUMN stripe_plan_id text;

-- Add indexes for better query performance
CREATE INDEX idx_user_profiles_stripe_customer_id ON user_profiles(stripe_customer_id);
CREATE INDEX idx_user_profiles_stripe_subscription_id ON user_profiles(stripe_subscription_id);
CREATE INDEX idx_user_profiles_stripe_subscription_status ON user_profiles(stripe_subscription_status);
