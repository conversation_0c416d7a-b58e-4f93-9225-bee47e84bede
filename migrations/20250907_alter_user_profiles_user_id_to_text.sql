-- Alter user_id column in user_profiles from UUID to TEXT for Clerk integration
ALTER TABLE user_profiles
  ALTER COLUMN user_id DROP DEFAULT,
  ALTER COLUMN user_id TYPE TEXT USING user_id::text,
  DROP CONSTRAINT IF EXISTS user_profiles_user_id_fkey;

-- Optionally, you may want to drop and recreate the foreign key if you have a users table with TEXT ids.
-- If not, just leave it as TEXT.

-- Update RLS policies if needed (should work as is since auth.uid() returns TEXT with Clerk JWT)
