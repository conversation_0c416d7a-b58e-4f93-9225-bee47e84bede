// Client-side Stripe configuration
export const getStripeJs = async () => {
  const { loadStripe } = await import('@stripe/stripe-js');
  return loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
};

// Server-side Stripe configuration (only use in API routes)
export const getServerStripe = () => {
  const Stripe = require('stripe');
  return new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2024-12-18.acacia',
    typescript: true,
  });
};

// Stripe Price IDs - Updated with actual price IDs for Weekly & Yearly plans
export const STRIPE_PLANS = {
  starter_weekly: 'price_1Rgc81PIVNHn8cqrcAcYaCGN',   // Starter Plan - Weekly
  starter_yearly: 'price_1Rgc8vPIVNHn8cqriwYHdXMy',   // Starter Plan - Yearly
  unlimited_weekly: 'price_1Rgc9ePIVNHn8cqrlQPacpPy', // Unlimited Plan - Weekly
  unlimited_yearly: 'price_1RgcBwPIVNHn8cqr3fshT47I', // Unlimited Plan - Yearly
} as const;

export type StripePlan = keyof typeof STRIPE_PLANS;

export const PLAN_FEATURES = {
  trial: {
    name: 'Free Trial',
    scans: 'Unlimited for 7 days',
    features: ['7-day free trial', 'Unlimited food analysis', 'Basic insights'],
  },
  starter: {
    name: 'Starter Plan',
    scans: 'Unlimited',
    features: ['Unlimited food analysis', 'Advanced insights', 'Export data'],
  },
  unlimited: {
    name: 'Unlimited Plan', 
    scans: 'Unlimited',
    features: ['Everything in Starter', 'Priority support', 'Advanced analytics'],
  },
} as const;
