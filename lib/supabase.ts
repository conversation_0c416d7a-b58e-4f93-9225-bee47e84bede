import { createClient } from '@supabase/supabase-js';
import { getAuth } from '@clerk/nextjs/server';
import { NextRequest } from 'next/server';

export function createClerkSupabaseClient(req?: NextRequest) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase URL or Anon Key environment variables.');
  }

  let supabaseClient = createClient(supabaseUrl, supabaseAnonKey);

  if (req) {
    const { sessionClaims } = getAuth(req);
    if (sessionClaims?.exp) {
      // Set the Supabase client's auth token to the Clerk session token
      supabaseClient.auth.setSession({
        access_token: sessionClaims.__session,
        expires_in: sessionClaims.exp - Math.floor(Date.now() / 1000),
        refresh_token: '', // Clerk session tokens are not refreshable by Supabase
        token_type: 'Bearer',
        user: {
          id: sessionClaims.sub?.toString() || '',
          aud: 'authenticated',
          role: 'authenticated',
          email: sessionClaims.email_address as string,
          phone: '',
          app_metadata: {},
          user_metadata: {},
          created_at: '',
          updated_at: '',
        },
      });
    }
  }

  return supabaseClient;
}
