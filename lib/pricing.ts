export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  weeklyPrice: number;
  yearlyPrice: number;
  weeklyPriceId: string;
  yearlyPriceId: string;
  features: string[];
  popular?: boolean;
  buttonText?: string;
  maxScansPerMonth?: number;
  dataRetentionDays?: number;
}

export const PRICING_CONFIG = {
  plans: [
    {
      id: 'free',
      name: 'Free 7 Days Trial',
      description: 'Always free',
      weeklyPrice: 0,
      yearlyPrice: 0,
      weeklyPriceId: '',
      yearlyPriceId: '',
      features: [
        'Complete Photo Analysis',
        '7 Days Meal Tracking',
        '1 Month Data Retention',
      ],
      buttonText: 'Switch to this plan',
      maxScansPerMonth: 0, // Unlimited during trial
      dataRetentionDays: 30,
    },
    {
      id: 'starter',
      name: 'Starter Plan',
      description: 'Premium Plan',
      weeklyPrice: 3.99,
      yearlyPrice: 71.88,
      weeklyPriceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_WEEKLY_PRICE_ID || '',
      yearlyPriceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_YEARLY_PRICE_ID || '',
      features: [
        '200 Instant Photo Analysis/Mo',
        'Basic Meal Tracking',
        'Basic Personalized Recommendations',
        'Standard Support',
        'No Commitment, Cancel Anytime',
      ],
      popular: true,
      buttonText: 'Switch to this plan',
      maxScansPerMonth: 200,
      dataRetentionDays: 365,
    },
    {
      id: 'unlimited',
      name: 'Unlimited Plan',
      description: 'Everything in Starter Plan',
      weeklyPrice: 5.99,
      yearlyPrice: 83.88,
      weeklyPriceId: process.env.NEXT_PUBLIC_STRIPE_UNLIMITED_WEEKLY_PRICE_ID || '',
      yearlyPriceId: process.env.NEXT_PUBLIC_STRIPE_UNLIMITED_YEARLY_PRICE_ID || '',
      features: [
        'Unlimited Instant Photo Analysis',
        'Comprehensive Personalized Recommendations',
        '365 Days Meal Tracking',
        'GI Index Monitoring',
        'Priority Support',
        'No Commitment, Cancel Anytime',
      ],
      buttonText: 'Switch to this plan',
      maxScansPerMonth: -1, // Unlimited
      dataRetentionDays: 365,
    },
  ] as PricingPlan[],
} as const;

export function getPlanById(planId: string): PricingPlan | undefined {
  return PRICING_CONFIG.plans.find(plan => plan.id === planId);
}

export function getPlanByPriceId(priceId: string): PricingPlan | undefined {
  return PRICING_CONFIG.plans.find(plan => 
    plan.weeklyPriceId === priceId || plan.yearlyPriceId === priceId
  );
}

export function calculateYearlySavings(weeklyPrice: number, yearlyPrice: number): number {
  return weeklyPrice * 52 - yearlyPrice;
}

export function getMonthlyEquivalent(yearlyPrice: number): number {
  return yearlyPrice / 12;
}

// Helper function to determine if a user has access to a feature based on their plan
export function hasFeatureAccess(userPlanId: string, feature: string): boolean {
  const plan = getPlanById(userPlanId);
  if (!plan) return false;
  
  return plan.features.some(f => f.toLowerCase().includes(feature.toLowerCase()));
}

// Helper function to check if user has reached scan limit
export function hasReachedScanLimit(userPlanId: string, currentScans: number): boolean {
  const plan = getPlanById(userPlanId);
  if (!plan) return true;
  
  // -1 means unlimited
  if (plan.maxScansPerMonth === -1) return false;
  
  return currentScans >= plan.maxScansPerMonth;
}
