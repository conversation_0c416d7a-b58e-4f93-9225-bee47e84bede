import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { useSession } from '@clerk/nextjs';
import { useEffect, useState } from 'react';

export function useClerkSupabase() {
  const { session } = useSession();
  const [supabase, setSupabase] = useState<SupabaseClient<any, 'public', any> | null>(null);

  useEffect(() => {
    if (session) {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

      if (!supabaseUrl || !supabaseAnonKey) {
        console.error('Missing Supabase URL or Anon Key environment variables.');
        return;
      }

      console.log("Clerk session:", session);
      const clerkToken = session.getToken({ template: 'supabase' });

      clerkToken.then((token) => {
        console.log("Clerk token for Supabase:", token);
        if (token) {
          const client = createClient(supabaseUrl, supabaseAnonKey, {
            global: {
              headers: { Authorization: `Bearer ${token}` },
            },
          });
          setSupabase(client);
        } else {
          console.error("Clerk token is null or undefined.");
        }
      }).catch((error) => {
        console.error("Error getting Clerk token:", error);
      });
    }
  }, [session]);

  return supabase;
}
