import i18nBase, { type i18n as I18nType } from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation files directly
import enTranslation from '../public/locales/en.json';
import zhTranslation from '../public/locales/zh.json';

export function createI18nInstance(language: string): I18nType {
  const instance = i18nBase.createInstance();
  instance
    .use(initReactI18next)
    .init({
      fallbackLng: 'en',
      lng: language,
      debug: false,
      interpolation: {
        escapeValue: false,
      },
      resources: {
        en: {
          translation: enTranslation,
        },
        zh: {
          translation: zhTranslation,
        },
      },
    });
  return instance;
}

// Default singleton for client-side use (will be mutated by I18nProvider if needed)
import i18n from 'i18next';
i18n
  .use(initReactI18next)
  .init({
    fallbackLng: 'en',
    lng: 'en',
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    resources: {
      en: {
        translation: enTranslation,
      },
      zh: {
        translation: zhTranslation,
      },
    },
  });

export default i18n;
